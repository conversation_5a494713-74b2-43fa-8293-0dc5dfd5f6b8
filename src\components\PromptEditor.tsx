import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { promptService } from '@/services/promptService';
import { excludedIdeasService } from '@/services/excludedIdeasService';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, Save, Settings, Eye, RefreshCw, RotateCcw } from 'lucide-react';
import type { Prompt } from '@/types/prompt';

interface PromptEditorProps {
  isOpen: boolean;
  onClose: () => void;
}

export function PromptEditor({ isOpen, onClose }: PromptEditorProps) {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [editedPrompts, setEditedPrompts] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [previewData, setPreviewData] = useState<{
    systemPrompt: string;
    userPrompt: string;
    existingProjects: string[];
    categories: string[];
    targetUsers: string[];
    excludedIdeas: Array<{title: string; description?: string}>;
    finalUserPrompt: string;
  } | null>(null);
  const [loadingPreview, setLoadingPreview] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen) {
      fetchPrompts();
    }
  }, [isOpen]);

  const fetchPrompts = async () => {
    setLoading(true);
    try {
      const fetchedPrompts = await promptService.getAllPrompts();
      setPrompts(fetchedPrompts);
      
      // Initialize edited prompts with current content
      const initialEdited: Record<string, string> = {};
      fetchedPrompts.forEach(prompt => {
        initialEdited[prompt.id] = prompt.content;
      });
      setEditedPrompts(initialEdited);
    } catch (error) {
      console.error('Error fetching prompts:', error);
      toast({
        title: "Error",
        description: "Failed to load prompts. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePromptChange = (promptId: string, content: string) => {
    setEditedPrompts(prev => ({
      ...prev,
      [promptId]: content
    }));
  };

  const handleSave = async (promptId: string) => {
    setSaving(true);
    try {
      const content = editedPrompts[promptId];
      await promptService.updatePrompt(promptId, { content });
      
      // Update the local state
      setPrompts(prev => prev.map(prompt => 
        prompt.id === promptId 
          ? { ...prompt, content, updated_at: new Date().toISOString() }
          : prompt
      ));

      toast({
        title: "Success",
        description: "Prompt updated successfully!",
      });
    } catch (error) {
      console.error('Error saving prompt:', error);
      toast({
        title: "Error",
        description: "Failed to save prompt. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleReset = (promptId: string) => {
    const originalPrompt = prompts.find(p => p.id === promptId);
    if (originalPrompt) {
      setEditedPrompts(prev => ({
        ...prev,
        [promptId]: originalPrompt.content
      }));
    }
  };

  const hasChanges = (promptId: string) => {
    const originalPrompt = prompts.find(p => p.id === promptId);
    return originalPrompt && editedPrompts[promptId] !== originalPrompt.content;
  };

  const systemPrompts = prompts.filter(p => p.type === 'system');
  const userPrompts = prompts.filter(p => p.type === 'user');

  const loadPreviewData = async () => {
    setLoadingPreview(true);
    try {
      // Get system prompt
      const systemPrompt = await promptService.getIdeaGenerationSystemPrompt();
      
      // Get base user prompt
      const baseUserPrompt = await promptService.getIdeaGenerationUserPrompt();
      
      // Get existing projects
      const { data: projectsData } = await supabase
        .from('projects')
        .select('name, category, target_user');
      
      const existingProjects = projectsData?.map(p => p.name) || [];
      const categories = [...new Set(projectsData?.map(p => p.category).filter(Boolean))] || [];
      const targetUsers = [...new Set(projectsData?.map(p => p.target_user).filter(Boolean))] || [];
      
      // Get excluded ideas
      const excludedIdeas = await excludedIdeasService.getExcludedIdeasWithDetails();
      
      // Build final user prompt
      const existingProjectsList = existingProjects.length > 0
        ? `\n\nExisting projects to avoid duplicating:\n${existingProjects.map(name => `- ${name}`).join('\n')}`
        : '';

      let categoriesAndTargetsInfo = '';
      if (categories.length > 0 || targetUsers.length > 0) {
        categoriesAndTargetsInfo = '\n\nExisting categories and target users in our database:';
        if (categories.length > 0) {
          categoriesAndTargetsInfo += `\nCategories: ${categories.join(', ')}`;
        }
        if (targetUsers.length > 0) {
          categoriesAndTargetsInfo += `\nTarget Users: ${targetUsers.join(', ')}`;
        }
        categoriesAndTargetsInfo += '\nPlease try to use similar categories and target users when appropriate, but also feel free to suggest new ones.';
      }

      const excludedIdeasList = excludedIdeas.length > 0
        ? `\n\nExcluded ideas to never suggest again:\n${excludedIdeas.map(idea => {
            const description = idea.description ? ` - ${idea.description}` : '';
            return `- ${idea.title}${description}`;
          }).join('\n')}`
        : '';

      const finalUserPrompt = `${baseUserPrompt}${existingProjectsList}${categoriesAndTargetsInfo}${excludedIdeasList}`;
      
      setPreviewData({
        systemPrompt,
        userPrompt: baseUserPrompt,
        existingProjects,
        categories,
        targetUsers,
        excludedIdeas,
        finalUserPrompt
      });
    } catch (error) {
      console.error('Error loading preview data:', error);
      toast({
        title: "Error",
        description: "Failed to load preview data",
        variant: "destructive",
      });
    } finally {
      setLoadingPreview(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Prompt Editor
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
              <p className="text-gray-600">Loading prompts...</p>
            </div>
          </div>
        ) : (
          <Tabs defaultValue="system" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="system">System Prompts</TabsTrigger>
              <TabsTrigger value="user">User Prompts</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="system" className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                System prompts define the AI's role and behavior. These are sent as system instructions to the AI model.
              </div>
              {systemPrompts.map((prompt) => (
                <Card key={prompt.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{prompt.name}</CardTitle>
                        <CardDescription>{prompt.description}</CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={prompt.is_active ? "default" : "secondary"}>
                          {prompt.is_active ? "Active" : "Inactive"}
                        </Badge>
                        <Badge variant="outline">System</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor={`prompt-${prompt.id}`}>Content</Label>
                      <Textarea
                        id={`prompt-${prompt.id}`}
                        value={editedPrompts[prompt.id] || ''}
                        onChange={(e) => handlePromptChange(prompt.id, e.target.value)}
                        className="min-h-[200px] mt-2 font-mono text-sm"
                        placeholder="Enter system prompt content..."
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => handleSave(prompt.id)}
                        disabled={!hasChanges(prompt.id) || saving}
                        size="sm"
                      >
                        {saving ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4 mr-1" />
                        )}
                        Save Changes
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleReset(prompt.id)}
                        disabled={!hasChanges(prompt.id)}
                        size="sm"
                      >
                        <RotateCcw className="h-4 w-4 mr-1" />
                        Reset
                      </Button>
                      {hasChanges(prompt.id) && (
                        <Badge variant="secondary" className="ml-2">
                          Unsaved changes
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="user" className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                User prompts define the specific request or task. These are sent as user messages to the AI model.
              </div>
              {userPrompts.map((prompt) => (
                <Card key={prompt.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{prompt.name}</CardTitle>
                        <CardDescription>{prompt.description}</CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={prompt.is_active ? "default" : "secondary"}>
                          {prompt.is_active ? "Active" : "Inactive"}
                        </Badge>
                        <Badge variant="outline">User</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor={`prompt-${prompt.id}`}>Content</Label>
                      <Textarea
                        id={`prompt-${prompt.id}`}
                        value={editedPrompts[prompt.id] || ''}
                        onChange={(e) => handlePromptChange(prompt.id, e.target.value)}
                        className="min-h-[300px] mt-2 font-mono text-sm"
                        placeholder="Enter user prompt content..."
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() => handleSave(prompt.id)}
                        disabled={!hasChanges(prompt.id) || saving}
                        size="sm"
                      >
                        {saving ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4 mr-1" />
                        )}
                        Save Changes
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleReset(prompt.id)}
                        disabled={!hasChanges(prompt.id)}
                        size="sm"
                      >
                        <RotateCcw className="h-4 w-4 mr-1" />
                        Reset
                      </Button>
                      {hasChanges(prompt.id) && (
                        <Badge variant="secondary" className="ml-2">
                          Unsaved changes
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="text-sm text-gray-600">
                  Preview of the final assembled prompt that will be sent to the AI
                </div>
                <Button
                  onClick={loadPreviewData}
                  disabled={loadingPreview}
                  size="sm"
                  variant="outline"
                >
                  {loadingPreview ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-1" />
                  )}
                  Refresh Preview
                </Button>
              </div>

              {loadingPreview ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : previewData ? (
                <div className="space-y-6">
                  <div className="text-sm text-gray-600 mb-4">
                    System Prompt:
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <pre className="!whitespace-pre-wrap">{previewData.systemPrompt}</pre>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    User Prompt:
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <pre className="!whitespace-pre-wrap">{previewData.userPrompt}</pre>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    Existing Projects:
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <pre className="!whitespace-pre-wrap">{previewData.existingProjects.join('\n')}</pre>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    Categories:
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <pre className="!whitespace-pre-wrap">{previewData.categories.join('\n')}</pre>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    Target Users:
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <pre className="!whitespace-pre-wrap">{previewData.targetUsers.join('\n')}</pre>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    Excluded Ideas:
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <pre className="!whitespace-pre-wrap">
                      {previewData.excludedIdeas.map(idea => `- ${idea.title}${idea.description ? ` - ${idea.description}` : ''}`).join('\n')}
                    </pre>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    Final User Prompt:
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg">
                    <pre className="!whitespace-pre-wrap">{previewData.finalUserPrompt}</pre>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">Click "Refresh Preview" to load the prompt preview</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}
      </DialogContent>
    </Dialog>
  );
}







