// Project content structure for JSON storage
export interface ProjectContent {
  description?: string;
  features?: string[];
  requirements?: string[];
  tech_stack?: string[];
  difficulty?: 'Easy' | 'Medium' | 'Hard';
  estimated_hours?: number;
  additional_notes?: string;
}

// Color scheme interface for AI-generated color codes
export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  success: string;
  warning: string;
  error: string;
  neutral: string;
  description: string;
}

// Main project interface
export interface Project {
  id: string;
  name: string;
  description: string;
  category: string;
  target_user: string;
  status: string;
  claimed_by: string | null;
  content: ProjectContent | null;
  pricing_tier: number;
  landing_page_image: string | null; // URL to generated landing page image stored in Supabase Storage
  color_code: ColorScheme | null; // AI-generated color scheme for the project
  created_at: string;
  updated_at: string;
}

// Default project content structure
export const defaultProjectContent: ProjectContent = {
  description: '',
  features: [],
  requirements: [],
  tech_stack: [],
  difficulty: 'Medium',
  estimated_hours: 40,
  additional_notes: ''
};

// Helper function to ensure content has proper structure
export const normalizeProjectContent = (content: any): ProjectContent => {
  if (!content || typeof content !== 'object') {
    return defaultProjectContent;
  }

  return {
    description: content.description || '',
    features: Array.isArray(content.features) ? content.features : [],
    requirements: Array.isArray(content.requirements) ? content.requirements : [],
    tech_stack: Array.isArray(content.tech_stack) ? content.tech_stack : [],
    difficulty: ['Easy', 'Medium', 'Hard'].includes(content.difficulty) ? content.difficulty : 'Medium',
    estimated_hours: typeof content.estimated_hours === 'number' ? content.estimated_hours : 40,
    additional_notes: content.additional_notes || ''
  };
};
