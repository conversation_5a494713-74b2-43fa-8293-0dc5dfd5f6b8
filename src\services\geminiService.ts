import { GoogleGenerativeAI, SchemaType } from '@google/generative-ai';

// MVP Generation Response Schema
interface DatabaseColumn {
  name: string;
  type: string;
  constraints?: string[];
  description?: string;
}

interface DatabaseTable {
  name: string;
  description: string;
  columns: DatabaseColumn[];
}

interface MVPResponse {
  project_description: {
    overview: string;
    problem_statement: string;
    solution: string;
  };
  features: string[];
  subscription_plan: {
    plan_name: string;
    price: string;
    features: string[];
  };
  homepage: {
    section_name: string;
    description: string;
  }[];
  main_user_features: {
    step_number: number;
    feature_name: string;
    description: string;
  }[];
  admin_features: string[];
  database_tables: DatabaseTable[];
  user_journey: string[];
  admin_workflow: string[];
  flow_diagram: string;
  notes: string[];
  complexity_tier: number;
}

interface ColorSchemeResponse {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  success: string;
  warning: string;
  error: string;
  neutral: string;
  description: string;
}

class GeminiService {
  private genAI: GoogleGenerativeAI;
  private readonly maxRetries = 10;
  private readonly retryDelay = 5000; // 5 seconds

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('VITE_GEMINI_API_KEY environment variable is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getResponseSchema() {
    return {
      type: SchemaType.OBJECT,
      required: [
        "project_description",
        "features",
        "subscription_plan",
        "homepage",
        "main_user_features",
        "admin_features",
        "database_tables",
        "user_journey",
        "admin_workflow",
        "flow_diagram",
        "notes",
        "complexity_tier"
      ],
      properties: {
        project_description: {
          type: SchemaType.OBJECT,
          required: ["overview", "problem_statement", "solution"],
          properties: {
            overview: { type: SchemaType.STRING, description: "Developer-focused description of what to build" },
            problem_statement: { type: SchemaType.STRING, description: "Clear description of the problem this SaaS solves" },
            solution: { type: SchemaType.STRING, description: "How this platform uniquely solves the problem" }
          }
        },
        features: {
          type: SchemaType.ARRAY,
          items: { type: SchemaType.STRING },
          description: "List of exact features that the platform will have (bullet point format)"
        },
        subscription_plan: {
          type: SchemaType.OBJECT,
          required: ["plan_name", "price", "features"],
          properties: {
            plan_name: { type: SchemaType.STRING, description: "Name of the subscription plan" },
            price: { type: SchemaType.STRING, description: "Monthly price (e.g., '$15/month')" },
            features: {
              type: SchemaType.ARRAY,
              items: { type: SchemaType.STRING },
              description: "List of plan features"
            }
          }
        },
        homepage: {
          type: SchemaType.ARRAY,
          items: {
            type: SchemaType.OBJECT,
            required: ["section_name", "description"],
            properties: {
              section_name: { type: SchemaType.STRING, description: "Name of the homepage section" },
              description: { type: SchemaType.STRING, description: "Description of what the section contains" }
            }
          }
        },
        main_user_features: {
          type: SchemaType.ARRAY,
          items: {
            type: SchemaType.OBJECT,
            required: ["step_number", "feature_name", "description"],
            properties: {
              step_number: { type: SchemaType.INTEGER, description: "Sequential step number" },
              feature_name: { type: SchemaType.STRING, description: "Name of the feature" },
              description: { type: SchemaType.STRING, description: "Detailed description of the feature" }
            }
          }
        },
        admin_features: {
          type: SchemaType.ARRAY,
          items: { type: SchemaType.STRING },
          description: "List of admin panel features"
        },
        database_tables: {
          type: SchemaType.ARRAY,
          items: {
            type: SchemaType.OBJECT,
            properties: {
              name: {
                type: SchemaType.STRING,
                description: "Table name"
              },
              description: {
                type: SchemaType.STRING,
                description: "Brief description of what this table stores"
              },
              columns: {
                type: SchemaType.ARRAY,
                items: {
                  type: SchemaType.OBJECT,
                  properties: {
                    name: {
                      type: SchemaType.STRING,
                      description: "Column name"
                    },
                    type: {
                      type: SchemaType.STRING,
                      description: "Data type (UUID, VARCHAR, TEXT, INTEGER, TIMESTAMP, ENUM, BOOLEAN, etc.)"
                    },
                    constraints: {
                      type: SchemaType.ARRAY,
                      items: { type: SchemaType.STRING },
                      description: "Constraints like PK, FK to table_name, NOT NULL, UNIQUE, etc."
                    },
                    description: {
                      type: SchemaType.STRING,
                      description: "Optional description of the column"
                    }
                  },
                  required: ["name", "type"]
                }
              }
            },
            required: ["name", "description", "columns"]
          },
          description: "Database schema with structured table and column definitions"
        },
        user_journey: {
          type: SchemaType.ARRAY,
          items: { type: SchemaType.STRING },
          description: "Step-by-step user journey through the platform"
        },
        admin_workflow: {
          type: SchemaType.ARRAY,
          items: { type: SchemaType.STRING },
          description: "Step-by-step admin workflow process"
        },
        flow_diagram: {
          type: SchemaType.STRING,
          description: "Mermaid flowchart diagram showing user and admin flows"
        },
        notes: {
          type: SchemaType.ARRAY,
          items: { type: SchemaType.STRING },
          description: "Additional implementation notes and guidelines"
        },
        complexity_tier: {
          type: SchemaType.INTEGER,
          description: "Project complexity tier: 50 (simple), 100 (standard), or 200 (complex)"
        }
      }
    };
  }

  private getSystemInstruction(): string {
    return `You are an expert SaaS product architect and MVP development specialist. You have extensive experience in designing scalable software-as-a-service platforms, understanding user needs, and creating comprehensive technical specifications by using very and extremly easy english for developer to understand and give to developer to build.

Your task is to analyze the given project name and generate a complete MVP (Minimum Viable Product) specification that includes all essential components for a successful SaaS launch. Focus on practical, implementable features that provide immediate value to users while maintaining scalability for future growth.

Consider modern SaaS best practices, user experience principles, and technical feasibility. Ensure your recommendations are actionable and suitable for a development team to implement within a reasonable timeframe.

Requirements:
- Start with a comprehensive project description explaining what the SaaS is about and its basic features that tells developers exactly what to build without explaining the project in detail, but do mention its features
- Generate a concise list of exact features that the platform will have (this will be displayed as bullet points below the solution section)
- Analyze project complexity and assign appropriate pricing tier based on development effort
- Make sure to include all the below essential features.
- Make sure to define the package subscription plan. Try to use 1 or 2 only. (its recommend to use only 1 if possible, but it depends on the project)
- If ever the project need AI integration, simply tell them to use Gemini API only, not OpenAI
- If ever the project require image AI integration, tell them to use cloudflare flux integration.
- Don't make the SaaS complicated to use
- Do not mention tech stack
- Keep it short and straight forward
- Define the flow + process how user will use it
- Create a Mermaid flowchart diagram showing the complete user and admin flow visually

## Project Complexity & Pricing Tiers:
**$50 Tier (Simple Projects):**
- Single-purpose tools (calculators, converters, generators)
- Basic CRUD operations with minimal features
- Simple input → output functionality
- 1-3 main features
- Basic user management only

**$100 Tier (Standard Projects):**
- Multi-feature applications
- User dashboards with moderate complexity
- Standard SaaS features (auth, billing, basic admin)
- 4-8 main features
- Some data management and reporting

**$200 Tier (Complex Projects):**
- Advanced multi-user platforms
- Complex workflows and automation
- Advanced analytics and reporting
- 8+ main features
- Complex integrations and data processing
- Advanced admin features and user roles

# Must-Have Features for All SaaS:

### For Users:
- Signup & Login (no email confirmation required)
- Manage Profile (view and edit profile, change password)
- Subscription and Billing
  - A. Plan options (one-time or recurring - but must only be monthly) - Hardcode it, no need to store in the database
  - B. Plan options (Try to use only 1 or 2 plan - depends on project)
  - Stripe integration for payments
  - View billing history
- Main User Interface:
  - IMPORTANT: Think carefully whether this app needs a separate /dashboard page OR if the main features can be integrated directly into the homepage after login
  - Simple apps (like text generators, calculators, single-purpose tools) work better with homepage integration
  - Complex apps (with multiple features, data management, analytics) need a separate dashboard
  - Choose the approach that makes the most sense for the specific project
  - See whether CMS is needed depending on the project type

### For Admins:
- Simple Admin panel to manage users only (view, edit, assign packages, etc.)
- No other admin features are required

### Homepage:
- Header with Signup/Login button
  → Show user’s name when logged in
- Hero section
- Main content section
  → For simple apps: Include the main functionality directly on homepage after login
  → For complex apps: Show overview and link to dashboard
- Footer with links (e.g., /documentation, /pricing, etc.)
  *(No need to build these pages - just show links)*

### Technologies:
- React (Frontend)
- Supabase (Database, auth, policies, edge functions, storage if needed)

### Don't Waste time on:
- Security, Performance
- Favicon, logo
- Forget Password page

## Rules:
- Avoid overloading the UI (homepage, admin panel) with too many icons. Most AI-generated code tends to add excessive icons, so keep it simple and use fewer icons.
- Don't leave any placeholders! Populate the site with data and don't add comming soon text.
- Based on project type, all data must be stored in Supabase and create all necessary tables
- No static data in frontend, make sure to use supabase to store them

## Homepage vs Dashboard Decision Guide:
**Use Homepage Integration for:**
- Text generators, AI assistants, calculators
- Single-purpose tools (QR generators, converters)
- Simple input → output apps
- Apps with minimal data to manage

**Use Separate Dashboard for:**
- Multi-feature platforms
- Apps with complex data management
- Analytics/reporting tools
- Content management systems
- Apps with multiple user roles/permissions`;
  }

  private getUserContent(projectName: string, criticalNotes?: string): string {
    const criticalNotesSection = criticalNotes
      ? `\n\n🚨 CRITICAL NOTES - MUST FOLLOW THESE REQUIREMENTS:\n${criticalNotes}\n\nThese critical notes are MANDATORY and must be incorporated into the MVP design. They take priority over standard requirements.`
      : '';

    return `Please write me an MVP for "${projectName}".${criticalNotesSection}

Please respond with a valid JSON object that follows this EXACT structure (match the ChatGPT format).

IMPORTANT:
- "main_user_features" should contain the main features/functionality that users will use (not just signup/login steps)
- "user_journey" describes the step-by-step user journey/flow through the platform
- "admin_features" lists the admin features/capabilities
- "admin_workflow" describes the admin workflow/process
- "complexity_tier" must be exactly 50, 100, or 200 based on project complexity analysis
- CRITICAL DECISION: Analyze if this app needs a separate dashboard or if features can be integrated into the homepage:
  * Simple apps (text generators, calculators, single-purpose tools) → integrate features into homepage after login
  * Complex apps (multiple features, data management, analytics, CMS) → use separate dashboard
  * In user_journey, specify whether user goes to dashboard OR stays on homepage after login
- COMPLEXITY ANALYSIS: Carefully evaluate the project and assign the appropriate tier:
  * Count main features, assess data complexity, evaluate integration requirements
  * Consider development time and technical complexity
  * Assign 50 for simple tools, 100 for standard SaaS, 200 for complex platforms
- Create a Mermaid flowchart diagram that visually shows the complete user journey and admin workflow. Use proper Mermaid syntax with:
  - flowchart TD (top-down) only
  - Clear node connections with arrows
  - Different colors/styles for user vs admin flows
  - Include key steps like signup, subscription, core features, admin management
  - Use proper Mermaid syntax with \\n for line breaks in the JSON string
- DATABASE DESIGN PRINCIPLES: Follow these CRITICAL database design best practices:

  **SEPARATION OF CONCERNS:**
  * NEVER mix payment provider details in user tables (NO stripe_customer_id, paypal_id in users)
  * Create separate payment_methods table for payment provider data
  * Keep user authentication separate from business logic
  * Separate core entities from their metadata/settings

  **PROPER TABLE STRUCTURE:**
  * Each table should have: name, description, and columns array
  * Each column should have: name, type, and optional constraints/description
  * Common types: UUID, VARCHAR, TEXT, INTEGER, TIMESTAMP, ENUM, BOOLEAN
  * Common constraints: PK, FK to table_name, NOT NULL, UNIQUE
  * Always include: id (UUID, PK), created_at (TIMESTAMP), updated_at (TIMESTAMP)

  **RECOMMENDED PATTERNS:**
  * users: Only auth + basic profile (id, email, name, created_at, updated_at)
  * user_profiles: Extended profile data (bio, preferences, settings)
  * subscriptions: Subscription status and plan info (user_id, plan_type, status, expires_at)
  * payment_methods: Payment provider data (user_id, provider_type, provider_customer_id, is_default)
  * billing_history: Transaction records (user_id, amount, status, provider_transaction_id)

  **FLEXIBILITY PRINCIPLES:**
  * Design for multiple payment providers (Stripe, PayPal, etc.)
  * Use provider_type ENUM instead of hardcoded provider names
  * Separate business logic from implementation details
  * Plan for future feature additions without breaking changes

**CRITICAL: NEVER put payment provider IDs directly in users table!**
❌ BAD: users.stripe_customer_id, users.paypal_id
✅ GOOD: payment_methods.provider_customer_id with provider_type

Structure:
{
  "project_description": {
    "overview": "Build this app that contains [specific features]. A clear, developer-focused description of what the SaaS is about and its basic features that tells developers exactly what to build without explaining the project in detail.",
    "problem_statement": "Clear description of the specific problem this SaaS solves for users",
    "solution": "How this platform uniquely solves the identified problem"
  },
  "subscription_plan": {
    "plan_name": "Single monthly plan",
    "price": "$15/month",
    "features": ["Unlimited access to all features", "Feature 2", "Feature 3"]
  },
  "homepage": [
    {
      "section_name": "Header",
      "description": "Signup/Login button (shows user name when logged in)"
    },
    {
      "section_name": "Hero Section",
      "description": "Brief intro and main value proposition"
    },
    {
      "section_name": "Main Content",
      "description": "For simple apps: Include main functionality here after login. For complex apps: Show feature overview and link to dashboard"
    },
    {
      "section_name": "Footer",
      "description": "Links to Documentation, Pricing, etc. (links only, no pages built)"
    }
  ],
  "main_user_features": [
    {
      "step_number": 1,
      "feature_name": "User Signup & Login",
      "description": "Simple email + password form, No email confirmation needed"
    },
    {
      "step_number": 2,
      "feature_name": "Subscription & Billing",
      "description": "Stripe integration for monthly payments, view billing history"
    },
    {
      "step_number": 3,
      "feature_name": "User Dashboard",
      "description": "Main interface where users access core features and manage their account"
    }
  ],
  "admin_features": [
    "View all users (list with name, email, subscription status)",
    "Edit user info (name, email)",
    "Assign or revoke subscription access (toggle active/inactive)"
  ],
  "database_tables": [
    {
      "name": "users",
      "description": "Core user authentication and basic profile (Supabase Auth integration)",
      "columns": [
        {
          "name": "id",
          "type": "UUID",
          "constraints": ["PK"],
          "description": "Primary key from Supabase Auth"
        },
        {
          "name": "email",
          "type": "VARCHAR",
          "constraints": ["UNIQUE", "NOT NULL"],
          "description": "User email address"
        },
        {
          "name": "full_name",
          "type": "VARCHAR",
          "description": "User's full name"
        },
        {
          "name": "created_at",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"],
          "description": "Account creation timestamp"
        },
        {
          "name": "updated_at",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"],
          "description": "Last profile update"
        }
      ]
    },
    {
      "name": "subscriptions",
      "description": "User subscription plans and status",
      "columns": [
        {
          "name": "id",
          "type": "UUID",
          "constraints": ["PK"]
        },
        {
          "name": "user_id",
          "type": "UUID",
          "constraints": ["FK to users", "NOT NULL"]
        },
        {
          "name": "plan_type",
          "type": "ENUM",
          "constraints": ["NOT NULL"],
          "description": "basic, pro, enterprise"
        },
        {
          "name": "status",
          "type": "ENUM",
          "constraints": ["NOT NULL"],
          "description": "active, inactive, cancelled, past_due"
        },
        {
          "name": "current_period_start",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"]
        },
        {
          "name": "current_period_end",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"]
        },
        {
          "name": "created_at",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"]
        },
        {
          "name": "updated_at",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"]
        }
      ]
    },
    {
      "name": "payment_methods",
      "description": "Payment provider information (supports multiple providers)",
      "columns": [
        {
          "name": "id",
          "type": "UUID",
          "constraints": ["PK"]
        },
        {
          "name": "user_id",
          "type": "UUID",
          "constraints": ["FK to users", "NOT NULL"]
        },
        {
          "name": "provider_type",
          "type": "ENUM",
          "constraints": ["NOT NULL"],
          "description": "stripe, paypal, apple_pay, google_pay"
        },
        {
          "name": "provider_customer_id",
          "type": "VARCHAR",
          "constraints": ["NOT NULL"],
          "description": "Customer ID from payment provider"
        },
        {
          "name": "is_default",
          "type": "BOOLEAN",
          "constraints": ["NOT NULL"],
          "description": "Default payment method for user"
        },
        {
          "name": "created_at",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"]
        },
        {
          "name": "updated_at",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"]
        }
      ]
    },
    {
      "name": "billing_history",
      "description": "Transaction history and payment records",
      "columns": [
        {
          "name": "id",
          "type": "UUID",
          "constraints": ["PK"]
        },
        {
          "name": "user_id",
          "type": "UUID",
          "constraints": ["FK to users", "NOT NULL"]
        },
        {
          "name": "subscription_id",
          "type": "UUID",
          "constraints": ["FK to subscriptions", "NOT NULL"]
        },
        {
          "name": "amount",
          "type": "INTEGER",
          "constraints": ["NOT NULL"],
          "description": "Amount in cents"
        },
        {
          "name": "currency",
          "type": "VARCHAR",
          "constraints": ["NOT NULL"],
          "description": "ISO currency code (USD, EUR, etc.)"
        },
        {
          "name": "status",
          "type": "ENUM",
          "constraints": ["NOT NULL"],
          "description": "pending, succeeded, failed, refunded"
        },
        {
          "name": "provider_transaction_id",
          "type": "VARCHAR",
          "description": "Transaction ID from payment provider"
        },
        {
          "name": "provider_type",
          "type": "ENUM",
          "constraints": ["NOT NULL"],
          "description": "stripe, paypal, apple_pay, google_pay"
        },
        {
          "name": "created_at",
          "type": "TIMESTAMP",
          "constraints": ["NOT NULL"]
        }
      ]
    },
    {
      "name": "user_preferences",
      "description": "User settings and preferences (separate from core auth)",
      "columns": [
        {
          "name": "id",
          "type": "UUID",
          "constraints": ["PK"]
        },
        {
          "name": "user_id",
          "type": "UUID",
          "constraints": ["FK to users", "NOT NULL"]
        },
        {
          "name": "module_id",
          "type": "UUID",
          "constraints": ["FK to modules", "NOT NULL"]
        },
        {
          "name": "completion_status",
          "type": "ENUM",
          "constraints": ["NOT NULL"],
          "description": "not_started, in_progress, completed"
        },
        {
          "name": "completed_at",
          "type": "TIMESTAMP"
        }
      ]
    }
  ],
  "user_journey": [
    "User visits homepage → signs up → logs in",
    "Chooses to subscribe by entering payment info → Stripe processes payment monthly",
    "For simple apps: Uses main features directly on homepage. For complex apps: Accesses dashboard",
    "Uses core features, saves/manages data as needed",
    "Can update profile and review billing history anytime"
  ],
  "admin_workflow": [
    "Login to admin panel",
    "Monitor user list and subscription statuses",
    "Edit user details or toggle subscription access as needed"
  ],
  "flow_diagram": "flowchart TD\n    direction LR\n    A[User visits homepage] --> B[Sign up]\n    B --> C[Subscribe via Stripe]\n    C --> D[Access Dashboard]\n    D --> E[Use core features]\n    E --> F[View billing history]\n    \n    G[Admin login] --> H[View users]\n    H --> I[Manage subscriptions]\n    \n    style A fill:#e1f5fe\n    style C fill:#f3e5f5\n    style G fill:#fff3e0",
  "notes": [
    "Simple, no-frills UI without excessive icons",
    "All content and progress stored in Supabase, dynamically loaded",
    "No password reset or email verification flows",
    "Stripe handles billing securely, integrated via Supabase edge functions",
    "Homepage with static links only (no extra pages built)",
    "When developing, always break them into smaller manageable components"
  ],
  "complexity_tier": 100
}`;
  }

  async generateMVP(projectName: string, criticalNotes?: string): Promise<MVPResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`Attempting to generate MVP for "${projectName}" (attempt ${attempt}/${this.maxRetries})`);

        const model = this.genAI.getGenerativeModel({
          model: "gemini-2.5-pro",
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40,
            maxOutputTokens: 65536,
            responseMimeType: "application/json",
            responseSchema: this.getResponseSchema(),
          },
          systemInstruction: this.getSystemInstruction(),
        });

        const userPrompt = this.getUserContent(projectName, criticalNotes);
        const result = await model.generateContent(userPrompt);
        const response = result.response;
        const text = response.text();

        if (!text.trim()) {
          throw new Error('Empty response from Gemini API');
        }

        const parsedResponse = JSON.parse(text);
        console.log('Successfully generated MVP:', parsedResponse);
        return parsedResponse as MVPResponse;

      } catch (error) {
        lastError = error as Error;
        console.error(`Attempt ${attempt} failed:`, error);

        if (attempt < this.maxRetries) {
          console.log(`Retrying in ${this.retryDelay / 1000} seconds...`);
          await this.delay(this.retryDelay);
        }
      }
    }

    throw new Error(`Failed to generate MVP after ${this.maxRetries} attempts. Last error: ${lastError?.message}`);
  }

  private getEnhancementUserContent(projectName: string, existingContent: any, enhancementRequests: string): string {
    const enhancementSection = enhancementRequests
      ? `\n\n🚀 ENHANCEMENT REQUESTS:\n${enhancementRequests}\n\nThese enhancement requests should be incorporated into the existing MVP while preserving all current functionality.`
      : '\n\nPlease enhance the existing MVP with general improvements, additional features, and better functionality while preserving all current features.';

    return `Please enhance the existing MVP for "${projectName}" by adding new features and improvements.${enhancementSection}

CURRENT MVP CONTENT (DO NOT REMOVE ANY EXISTING FUNCTIONALITY):
${JSON.stringify(existingContent, null, 2)}

ENHANCEMENT INSTRUCTIONS:
1. Keep ALL existing functionality and features intact
2. Add the requested enhancements or general improvements if no specific requests provided
3. Ensure new features integrate seamlessly with existing ones
4. Update database tables if new features require additional data storage
5. Update user journey and admin workflow to include new features
6. Maintain the same JSON structure and format
7. Update the flow diagram to include new features
8. Ensure complexity tier is appropriate for the enhanced feature set

Please respond with the enhanced JSON object that follows the EXACT same structure as the current content but with added features and improvements.`;
  }

  async enhanceMVP(projectName: string, existingContent: any, enhancementRequests?: string): Promise<MVPResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`Attempting to enhance MVP for "${projectName}" (attempt ${attempt}/${this.maxRetries})`);

        const model = this.genAI.getGenerativeModel({
          model: "gemini-2.5-pro",
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40,
            maxOutputTokens: 65536,
            responseMimeType: "application/json",
            responseSchema: this.getResponseSchema(),
          },
          systemInstruction: this.getSystemInstruction(),
        });

        const userPrompt = this.getEnhancementUserContent(projectName, existingContent, enhancementRequests || '');
        const result = await model.generateContent(userPrompt);
        const response = result.response;
        const text = response.text();

        if (!text.trim()) {
          throw new Error('Empty response from Gemini API');
        }

        const parsedResponse = JSON.parse(text);
        console.log('Successfully enhanced MVP:', parsedResponse);
        return parsedResponse as MVPResponse;

      } catch (error) {
        lastError = error as Error;
        console.error(`Enhancement attempt ${attempt} failed:`, error);

        if (attempt < this.maxRetries) {
          console.log(`Retrying in ${this.retryDelay / 1000} seconds...`);
          await this.delay(this.retryDelay);
        }
      }
    }

    throw new Error(`Failed to enhance MVP after ${this.maxRetries} attempts. Last error: ${lastError?.message}`);
  }

  // Generate landing page prompt based on project content
  async generateLandingPagePrompt(projectName: string, projectDescription: string, projectContent: any): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`Attempting to generate landing page prompt for "${projectName}" (attempt ${attempt}/${this.maxRetries})`);

        const model = this.genAI.getGenerativeModel({
          model: "gemini-2.5-pro",
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40,
            maxOutputTokens: 65536,
            responseMimeType: "text/plain",
          },
          systemInstruction: this.getLandingPagePromptSystemInstruction(),
        });

        const userPrompt = this.getLandingPagePromptUserContent(projectName, projectDescription, projectContent);
        const result = await model.generateContent(userPrompt);
        const response = result.response;
        const text = response.text();

        if (!text.trim()) {
          throw new Error('Empty response from Gemini API');
        }

        console.log('Successfully generated landing page prompt');
        return text.trim();

      } catch (error) {
        lastError = error as Error;
        console.error(`Attempt ${attempt} failed:`, error);

        if (attempt < this.maxRetries) {
          console.log(`Retrying in ${this.retryDelay / 1000} seconds...`);
          await this.delay(this.retryDelay);
        }
      }
    }

    throw new Error(`Failed to generate landing page prompt after ${this.maxRetries} attempts. Last error: ${lastError?.message}`);
  }

  private getLandingPagePromptSystemInstruction(): string {
    return `You are an expert UI/UX designer creating prompts for AI image generation of COMPLETE SaaS landing pages.

CRITICAL: Your response must be under 2000 characters total.

Create a prompt for a FULL-LENGTH landing page that includes ALL sections from top to bottom:
- Header with navigation and logo
- Hero section with headline and CTA
- Features/benefits section with cards/icons
- Pricing section or additional content
- Footer with links and contact info

Specify that it should be a complete, scrollable landing page view showing the entire page layout in ultra HD quality. Focus on modern SaaS design with professional aesthetics.`;
  }

  private getLandingPagePromptUserContent(projectName: string, projectDescription: string, projectContent: any): string {
    // Extract key information from project content
    const features = projectContent?.main_user_features?.slice(0, 3)?.map((f: any) => f.feature_name) || [];
    const targetUser = projectContent?.project_description?.problem_statement || projectDescription;
    const pricing = projectContent?.subscription_plan?.price || '$10/month';

    return `Create a prompt for a COMPLETE full-length SaaS landing page (under 2000 chars):

**Product:** ${projectName}
**Purpose:** ${targetUser}
**Key Features:** ${features.join(', ')}
**Pricing:** ${pricing}

Generate a prompt for a FULL LANDING PAGE showing the ENTIRE page from header to footer in ultra HD quality:

Must include ALL sections vertically:
1. Header with navigation bar and logo
2. Hero section with headline and CTA button
3. Features section with 3-4 feature cards/icons
4. Pricing section or testimonials
5. Footer with links and contact info

Specify: "Complete full-length landing page mockup, ultra HD quality, modern SaaS design, professional color scheme, showing entire scrollable page layout from top to bottom, desktop view, high resolution, detailed UI elements"`;
  }

  // Color scheme generation methods
  private getColorSchemeSystemInstruction(): string {
    return `You are an expert UI/UX designer specializing in creating professional and UNIQUE color schemes for SaaS applications.

Your task is to generate a comprehensive color palette that:
- Reflects the project's purpose and target audience
- Follows modern design principles and accessibility standards
- Provides good contrast ratios for readability
- Creates a professional and trustworthy appearance
- Includes semantic colors for different UI states
- MOST IMPORTANTLY: Creates a visually distinct and unique color scheme that stands out from existing projects

UNIQUENESS STRATEGY:
- When existing colors are provided, analyze their hue, saturation, and brightness
- Choose colors that are significantly different in the color wheel (at least 30-60 degrees apart)
- If warm colors are already used, consider cool colors and vice versa
- Explore different color harmonies: complementary, triadic, split-complementary, or analogous
- Consider unique color combinations that are rarely used in SaaS applications
- Think beyond typical blue/green tech colors - explore purples, oranges, teals, magentas, etc.

Generate colors in HEX format (#RRGGBB) and provide a brief description explaining the color choices, how they reflect the project's brand identity, and how they ensure uniqueness from existing projects.

Focus on creating a cohesive color system that would work well for a modern SaaS application interface while being distinctly different from other projects.`;
  }

  private getColorSchemeUserContent(projectName: string, projectDescription: string, category: string, targetUser: string, existingColorCodes: string[]): string {
    const existingColorsSection = existingColorCodes.length > 0
      ? `\n\nIMPORTANT - AVOID THESE EXISTING COLOR SCHEMES:
The following primary colors have already been used in other projects. Please create a UNIQUE color scheme that is distinctly different:
${existingColorCodes.map(color => `- ${color}`).join('\n')}

UNIQUENESS REQUIREMENTS:
- Your primary color must be at least 30 degrees different in hue from any existing primary color
- Avoid similar color families (e.g., if blue tones are used, try warm colors like orange/red/yellow)
- Create a fresh, distinctive palette that stands out from existing projects
- Consider complementary or triadic color harmonies for maximum differentiation`
      : '\n\nThis is one of the first projects, so you have full creative freedom with color selection.';

    return `Generate a professional and UNIQUE color scheme for the SaaS project: "${projectName}"

Project Details:
- Description: ${projectDescription}
- Category: ${category}
- Target User: ${targetUser}${existingColorsSection}

Please respond with a valid JSON object that follows this EXACT structure:

{
  "primary": "#RRGGBB",
  "secondary": "#RRGGBB",
  "accent": "#RRGGBB",
  "background": "#RRGGBB",
  "text": "#RRGGBB",
  "success": "#RRGGBB",
  "warning": "#RRGGBB",
  "error": "#RRGGBB",
  "neutral": "#RRGGBB",
  "description": "Brief explanation of the color choices and how they reflect the project's brand identity and ensure uniqueness from existing projects"
}

Requirements:
- All colors must be in HEX format (#RRGGBB)
- Ensure good contrast ratios for accessibility
- Primary color should reflect the project's core purpose
- Colors should appeal to the target user demographic
- Create a professional, modern SaaS appearance
- MOST IMPORTANT: Ensure the color scheme is visually distinct and unique from existing projects`;
  }

  private getColorSchemeResponseSchema() {
    return {
      type: SchemaType.OBJECT,
      required: ["primary", "secondary", "accent", "background", "text", "success", "warning", "error", "neutral", "description"],
      properties: {
        primary: { type: SchemaType.STRING, description: "Primary brand color in HEX format" },
        secondary: { type: SchemaType.STRING, description: "Secondary brand color in HEX format" },
        accent: { type: SchemaType.STRING, description: "Accent color for highlights in HEX format" },
        background: { type: SchemaType.STRING, description: "Main background color in HEX format" },
        text: { type: SchemaType.STRING, description: "Primary text color in HEX format" },
        success: { type: SchemaType.STRING, description: "Success state color in HEX format" },
        warning: { type: SchemaType.STRING, description: "Warning state color in HEX format" },
        error: { type: SchemaType.STRING, description: "Error state color in HEX format" },
        neutral: { type: SchemaType.STRING, description: "Neutral/muted color in HEX format" },
        description: { type: SchemaType.STRING, description: "Explanation of color choices and brand identity" }
      }
    };
  }

  // Helper method to get existing color codes from database
  private async getExistingColorCodes(): Promise<string[]> {
    try {
      // This would typically use Supabase client, but since we're in the service layer,
      // we'll return a placeholder. In a real implementation, you'd inject the Supabase client
      // or make this method accept existing colors as a parameter
      return [];
    } catch (error) {
      console.error('Error fetching existing color codes:', error);
      return [];
    }
  }

  // Generate color scheme for a project with uniqueness check
  async generateColorScheme(projectName: string, projectDescription: string, category: string, targetUser: string, existingColorCodes?: string[]): Promise<ColorSchemeResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`Attempting to generate color scheme for "${projectName}" (attempt ${attempt}/${this.maxRetries})`);

        const model = this.genAI.getGenerativeModel({
          model: "gemini-2.5-pro",
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40,
            maxOutputTokens: 65536,
            responseMimeType: "application/json",
            responseSchema: this.getColorSchemeResponseSchema(),
          },
          systemInstruction: this.getColorSchemeSystemInstruction(),
        });

        const userPrompt = this.getColorSchemeUserContent(projectName, projectDescription, category, targetUser, existingColorCodes || []);
        const result = await model.generateContent(userPrompt);
        const response = result.response;
        const text = response.text();

        if (!text.trim()) {
          throw new Error('Empty response from Gemini API');
        }

        const parsedResponse = JSON.parse(text);
        console.log('Successfully generated color scheme:', parsedResponse);
        return parsedResponse as ColorSchemeResponse;

      } catch (error) {
        lastError = error as Error;
        console.error(`Attempt ${attempt} failed:`, error);

        if (attempt < this.maxRetries) {
          console.log(`Retrying in ${this.retryDelay / 1000} seconds...`);
          await this.delay(this.retryDelay);
        }
      }
    }

    throw new Error(`Failed to generate color scheme after ${this.maxRetries} attempts. Last error: ${lastError?.message}`);
  }
}

export const geminiService = new GeminiService();
export type { MVPResponse, ColorSchemeResponse };

