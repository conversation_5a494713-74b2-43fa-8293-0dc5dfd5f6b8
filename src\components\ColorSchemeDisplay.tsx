import React from 'react';
import { <PERSON>, <PERSON>Content, CardH<PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Palette, Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { ColorScheme } from '@/types/project';

interface ColorSchemeDisplayProps {
  colorScheme: ColorScheme;
  projectName: string;
}

export function ColorSchemeDisplay({ colorScheme, projectName }: ColorSchemeDisplayProps) {
  const [copiedColor, setCopiedColor] = React.useState<string | null>(null);

  const copyToClipboard = async (color: string, colorName: string) => {
    try {
      await navigator.clipboard.writeText(color);
      setCopiedColor(color);
      toast({
        title: "Copied!",
        description: `${colorName} color (${color}) copied to clipboard`,
      });
      
      // Reset the copied state after 2 seconds
      setTimeout(() => setCopiedColor(null), 2000);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy color to clipboard",
        variant: "destructive",
      });
    }
  };

  const colorItems = [
    { name: 'Primary', value: colorScheme.primary, description: 'Main brand color' },
    { name: 'Secondary', value: colorScheme.secondary, description: 'Supporting brand color' },
    { name: 'Accent', value: colorScheme.accent, description: 'Highlight color' },
    { name: 'Background', value: colorScheme.background, description: 'Main background' },
    { name: 'Text', value: colorScheme.text, description: 'Primary text color' },
    { name: 'Success', value: colorScheme.success, description: 'Success states' },
    { name: 'Warning', value: colorScheme.warning, description: 'Warning states' },
    { name: 'Error', value: colorScheme.error, description: 'Error states' },
    { name: 'Neutral', value: colorScheme.neutral, description: 'Neutral/muted color' },
  ];

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5 text-purple-600" />
          Color Scheme for {projectName}
        </CardTitle>
        {colorScheme.description && (
          <p className="text-sm text-gray-600 mt-2">{colorScheme.description}</p>
        )}
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {colorItems.map((item) => (
            <div
              key={item.name}
              className="group relative border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => copyToClipboard(item.value, item.name)}
            >
              <div className="flex items-center gap-3">
                {/* Color Preview */}
                <div
                  className="w-12 h-12 rounded-lg border-2 border-gray-200 shadow-sm flex-shrink-0"
                  style={{ backgroundColor: item.value }}
                />
                
                {/* Color Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium text-gray-900">{item.name}</h3>
                    <Badge variant="outline" className="text-xs">
                      {item.value}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{item.description}</p>
                </div>

                {/* Copy Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation();
                    copyToClipboard(item.value, item.name);
                  }}
                >
                  {copiedColor === item.value ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Hover overlay */}
              <div className="absolute inset-0 bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg pointer-events-none" />
            </div>
          ))}
        </div>

        {/* Color Palette Preview */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Color Palette Preview</h4>
          <div className="flex flex-wrap gap-2">
            {colorItems.map((item) => (
              <div
                key={`preview-${item.name}`}
                className="group relative"
                title={`${item.name}: ${item.value}`}
              >
                <div
                  className="w-8 h-8 rounded-full border-2 border-white shadow-sm cursor-pointer hover:scale-110 transition-transform"
                  style={{ backgroundColor: item.value }}
                  onClick={() => copyToClipboard(item.value, item.name)}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-xs text-blue-800">
            💡 <strong>Tip:</strong> Click on any color to copy its hex code to your clipboard. 
            Use these colors in your design tools or CSS to maintain brand consistency.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
