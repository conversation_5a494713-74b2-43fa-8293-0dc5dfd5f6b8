import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Lightbulb, Save, X, Ban } from 'lucide-react';
import { SaasIdea } from '@/services/ideaGenerationService';
import { excludedIdeasService } from '@/services/excludedIdeasService';
import { toast } from '@/hooks/use-toast';

interface IdeaGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  ideas: SaasIdea[];
  isLoading: boolean;
  onSaveSelected: (selectedIdeas: SaasIdea[]) => void;
}

export function IdeaGenerationModal({
  isOpen,
  onClose,
  ideas,
  isLoading,
  onSaveSelected
}: IdeaGenerationModalProps) {
  const [selectedIdeas, setSelectedIdeas] = useState<Set<number>>(new Set());
  const [editedIdeas, setEditedIdeas] = useState<SaasIdea[]>([]);
  const [excludedIdeas, setExcludedIdeas] = useState<Set<number>>(new Set());
  const [isExcluding, setIsExcluding] = useState(false);

  // Initialize edited ideas when ideas change
  useEffect(() => {
    setEditedIdeas([...ideas]);
  }, [ideas]);

  const handleIdeaToggle = (index: number) => {
    // Don't allow selecting excluded ideas
    if (excludedIdeas.has(index)) {
      return;
    }

    const newSelected = new Set(selectedIdeas);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedIdeas(newSelected);
  };

  const handleSelectAll = () => {
    const selectableIdeas = editedIdeas
      .map((_, index) => index)
      .filter(index => !excludedIdeas.has(index));

    if (selectedIdeas.size === selectableIdeas.length) {
      setSelectedIdeas(new Set());
    } else {
      setSelectedIdeas(new Set(selectableIdeas));
    }
  };

  const handleIdeaEdit = (index: number, field: keyof SaasIdea, value: string) => {
    const newEditedIdeas = [...editedIdeas];
    newEditedIdeas[index] = {
      ...newEditedIdeas[index],
      [field]: value
    };
    setEditedIdeas(newEditedIdeas);
  };

  const handleSave = () => {
    const selected = Array.from(selectedIdeas).map(index => editedIdeas[index]);
    onSaveSelected(selected);
    setSelectedIdeas(new Set());
  };

  const handleExcludeIdea = async (index: number) => {
    const idea = editedIdeas[index];
    setIsExcluding(true);

    try {
      await excludedIdeasService.addExcludedIdea({
        title: idea.title,
        description: idea.description,
        category: idea.category,
        target_user: idea.target_user,
        reason: 'Manually excluded from idea generation modal'
      });

      // Add to excluded set for UI feedback
      const newExcluded = new Set(excludedIdeas);
      newExcluded.add(index);
      setExcludedIdeas(newExcluded);

      // Remove from selected if it was selected
      const newSelected = new Set(selectedIdeas);
      newSelected.delete(index);
      setSelectedIdeas(newSelected);

      toast({
        title: "Idea Excluded",
        description: `"${idea.title}" has been added to the exclusion list and won't appear in future generations.`,
      });
    } catch (error) {
      console.error('Error excluding idea:', error);
      toast({
        title: "Error",
        description: "Failed to exclude idea. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExcluding(false);
    }
  };

  const handleClose = () => {
    setSelectedIdeas(new Set());
    setExcludedIdeas(new Set());
    setEditedIdeas([...ideas]); // Reset to original ideas
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-yellow-500" />
            Generated SaaS Ideas
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
              <p className="text-gray-600">Generating amazing SaaS ideas...</p>
              <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
            </div>
          </div>
        ) : editedIdeas.length > 0 ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                >
                  {selectedIdeas.size === (editedIdeas.length - excludedIdeas.size) ? 'Deselect All' : 'Select All'}
                </Button>
                <span className="text-sm text-gray-600">
                  {selectedIdeas.size} of {editedIdeas.length - excludedIdeas.size} selected
                  {excludedIdeas.size > 0 && (
                    <span className="text-red-600 ml-2">
                      ({excludedIdeas.size} excluded)
                    </span>
                  )}
                </span>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={handleClose}
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={selectedIdeas.size === 0}
                >
                  <Save className="h-4 w-4 mr-1" />
                  Save Selected ({selectedIdeas.size})
                </Button>
              </div>
            </div>

            <div className="grid gap-4 max-h-[60vh] overflow-y-auto pr-2">
              {editedIdeas.map((idea, index) => (
                <Card
                  key={index}
                  className={`transition-all ${
                    excludedIdeas.has(index)
                      ? 'ring-2 ring-red-500 bg-red-50 opacity-75'
                      : selectedIdeas.has(index)
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:shadow-md'
                  }`}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="mb-2">
                          <div className="flex items-center gap-2 text-lg font-semibold">
                            <input
                              value={idea.title}
                              onChange={(e) => handleIdeaEdit(index, 'title', e.target.value)}
                              className="bg-transparent border-none p-0 outline-none focus:ring-0 font-semibold text-lg min-w-0 flex-shrink"
                              placeholder="Project title..."
                              style={{ width: `${Math.max(idea.title.length * 8 + 20, 120)}px` }}
                            />
                            <span className="text-gray-400">-</span>
                            <input
                              value={idea.summary}
                              onChange={(e) => handleIdeaEdit(index, 'summary', e.target.value)}
                              className="bg-transparent border-none p-0 outline-none focus:ring-0 text-gray-600 flex-1 min-w-0"
                              placeholder="Short summary..."
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                          {idea.category}
                        </Badge>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          {idea.target_user}
                        </Badge>
                        {excludedIdeas.has(index) ? (
                          <Badge variant="destructive" className="bg-red-100 text-red-700 border-red-200">
                            Excluded
                          </Badge>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleExcludeIdea(index)}
                            disabled={isExcluding}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            Exclude
                          </Button>
                        )}
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Textarea
                      value={idea.description}
                      onChange={(e) => handleIdeaEdit(index, 'description', e.target.value)}
                      className="text-gray-700 leading-relaxed border-none bg-transparent p-0 resize-none focus-visible:ring-0 focus-visible:ring-offset-0 min-h-[60px]"
                      placeholder="Project description..."
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No ideas generated yet.</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}



