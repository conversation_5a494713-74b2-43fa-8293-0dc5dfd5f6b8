import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AdminPanel } from "@/components/AdminPanel";
import { Button } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { BookOpen, Sparkles, MessageSquare, Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Admin = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);

  useEffect(() => {
    const isAuthenticated = localStorage.getItem("adminAuth") === "true";
    if (!isAuthenticated) {
      navigate("/admin/login");
    }
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem("adminAuth");
    navigate("/admin/login");
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "Message copied to clipboard",
    });
  };

  const upworkMessage = `Thanks for your interest. Here's the list of projects, pick one you'd like to build:
https://project-blink.onrender.com/

Let me know your choice and we'll move forward.`;

  const projectSelectedMessage = `Great! Now, can you please explain what you understood about this project?
Also, give me a step-by-step plan of how you will work to complete it.

I just want to know your opinion. It helps me understand how you think and analyze the project.

After that, I will send you a link to the "Tips" section. It will help you understand how to speed up the development.`;

  const giveAccessToTipsMessage = `Perfect! The project details already contain all the necessary information. If you have any questions later, you may ask.

Now, please go to https://project-blink.onrender.com/tips-login and log in using these credentials:
Username: vibecoderX
Password: vibecoderX

This tips section is meant to help you complete the project faster. If you're not familiar with the methods there, it's up to you whether you want to spend some time learning them now and use them repeatedly, or stick with your own approach... the choice is yours.

Once you have reviewed the tips, please confirm, and I'll send you the offer so you can proceed.

Remember to always refer back to the project details, as they include important instructions.`;

  return (
    <div className="min-h-screen bg-background">
      <div className="p-4 border-b border-border bg-gradient-to-r from-slate-50 to-gray-50">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold">Admin Panel</h1>
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => navigate("/")}>
              View Projects
            </Button>

            {/* Upwork Messages Button */}
            <Dialog open={isMessageModalOpen} onOpenChange={setIsMessageModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Upwork Messages
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Upwork Communication Templates</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="interest-response">
                      <AccordionTrigger>Step 1: When someone shows interest, send this:</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="bg-gray-50 p-4 rounded-lg border">
                            <pre className="whitespace-pre-wrap text-sm font-mono">{upworkMessage}</pre>
                          </div>
                          <Button
                            onClick={() => copyToClipboard(upworkMessage)}
                            className="w-full"
                            variant="outline"
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copy Message
                          </Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="project-selected">
                      <AccordionTrigger>Step 2: After they select a project, send this:</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <pre className="whitespace-pre-wrap text-sm font-mono">{projectSelectedMessage}</pre>
                          </div>
                          <Button
                            onClick={() => copyToClipboard(projectSelectedMessage)}
                            className="w-full"
                            variant="outline"
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copy Message
                          </Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="give-access-tips">
                      <AccordionTrigger>Step 3: Give access to tips</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                            <pre className="whitespace-pre-wrap text-sm font-mono">{giveAccessToTipsMessage}</pre>
                          </div>
                          <Button
                            onClick={() => copyToClipboard(giveAccessToTipsMessage)}
                            className="w-full"
                            variant="outline"
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copy Message
                          </Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              </DialogContent>
            </Dialog>

            {/* Private Tips Button - Eye-catching design */}
            <div className="relative">
              <Button
                onClick={() => window.open('/tips-login', '_blank')}
                className="private-tips-button relative overflow-hidden bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 text-white font-bold px-6 py-2 rounded-full shadow-2xl hover:shadow-purple-500/25 transform hover:scale-110 transition-all duration-300 border-2 border-white/20"
              >
                <div className="relative flex items-center gap-2 z-10">
                  <Sparkles className="h-4 w-4 animate-spin text-yellow-300" />
                  <BookOpen className="h-4 w-4" />
                  <span className="font-bold">Private Tips</span>
                  <Sparkles className="h-4 w-4 animate-spin text-yellow-300" style={{ animationDirection: 'reverse', animationDelay: '0.5s' }} />
                </div>

                {/* Shimmer overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 animate-pulse"></div>
              </Button>

              {/* Floating badge */}
              <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full animate-bounce shadow-lg">
                NEW!
              </div>
            </div>

            <Button variant="outline" onClick={handleLogout}>
              Logout
            </Button>
          </div>
        </div>
      </div>
      <AdminPanel />
    </div>
  );
};

export default Admin;