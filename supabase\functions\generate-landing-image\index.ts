import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ImageRequest {
  prompt: string;
  steps?: number;
  model?: string;
}

interface FluxResponse {
  result: {
    image: string; // Base64 encoded image
  };
  success: boolean;
  errors: any[];
  messages: any[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { prompt, steps = 6, model = '@cf/black-forest-labs/flux-1-schnell' }: ImageRequest = await req.json()

    if (!prompt) {
      return new Response(
        JSON.stringify({ error: 'Prompt is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get Cloudflare credentials from environment variables
    const CLOUDFLARE_API_TOKEN = Deno.env.get('CLOUDFLARE_API_TOKEN')
    const CLOUDFLARE_ACCOUNT_ID = Deno.env.get('CLOUDFLARE_ACCOUNT_ID')

    if (!CLOUDFLARE_API_TOKEN || !CLOUDFLARE_ACCOUNT_ID) {
      return new Response(
        JSON.stringify({ error: 'Cloudflare credentials not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log(`Generating image using ${model} for prompt: ${prompt.substring(0, 100)}...`)

    // Determine request body based on model
    let requestBody: any = { prompt }

    if (model.includes('flux')) {
      // Flux model uses 'steps' parameter
      requestBody.steps = steps
    } else if (model.includes('stable-diffusion-xl-lightning')) {
      // SDXL-Lightning uses 'num_steps' parameter
      requestBody.num_steps = Math.min(steps, 20) // SDXL max is 20
      requestBody.guidance = 7.5
    }

    // Call Cloudflare AI API
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/ai/run/${model}`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`Cloudflare API error: ${response.status} ${response.statusText} - ${errorText}`)
      return new Response(
        JSON.stringify({
          error: `Cloudflare API error: ${response.status} ${response.statusText}`,
          details: errorText
        }),
        {
          status: response.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Successfully generated image, uploading to storage...')

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    let imageBuffer: Uint8Array
    let contentType: string

    // Handle different response formats based on model
    if (model.includes('flux')) {
      // Flux returns JSON with base64 image
      const data: FluxResponse = await response.json()

      if (!data.success) {
        console.error('Cloudflare API returned error:', data.errors)
        return new Response(
          JSON.stringify({
            error: 'Cloudflare API returned error',
            details: data.errors
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!data.result?.image) {
        return new Response(
          JSON.stringify({ error: 'No image data returned from Cloudflare API' }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Convert base64 to binary
      imageBuffer = Uint8Array.from(atob(data.result.image), c => c.charCodeAt(0))
      contentType = 'image/jpeg'
    } else {
      // SDXL-Lightning returns binary image data directly
      imageBuffer = new Uint8Array(await response.arrayBuffer())
      contentType = 'image/png'
    }

    // Generate unique filename with correct extension
    const extension = contentType === 'image/png' ? 'png' : 'jpeg'
    const filename = `landing-page-${Date.now()}-${Math.random().toString(36).substring(7)}.${extension}`

    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from('landing-page-images')
      .upload(filename, imageBuffer, {
        contentType,
        cacheControl: '3600'
      })

    if (uploadError) {
      console.error('Storage upload error:', uploadError)
      return new Response(
        JSON.stringify({
          error: 'Failed to upload image to storage',
          details: uploadError.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('landing-page-images')
      .getPublicUrl(filename)

    console.log('Image uploaded successfully:', urlData.publicUrl)

    // Return the public URL
    return new Response(
      JSON.stringify({
        success: true,
        imageUrl: urlData.publicUrl
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Error in generate-landing-image function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
