import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Circle, DollarSign, Users, Database, Settings, FileText, Shield, Table, ArrowRight, Play, Key, Link, GitBranch } from 'lucide-react';
import { MermaidDiagram } from '@/components/MermaidDiagram';
import { ProjectDescription } from '@/components/ProjectDescription';

interface FlexibleJsonRendererProps {
  data: any;
  title?: string;
  projectName?: string;
  project?: any;
}

export function FlexibleJsonRenderer({ data, title, projectName, project }: FlexibleJsonRendererProps) {
  if (!data || typeof data !== 'object') {
    return null;
  }

  const formatKey = (key: string): string => {
    return key
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  };

  const getIconForKey = (key: string) => {
    const lowerKey = key.toLowerCase();
    if (lowerKey.includes('subscription') || lowerKey.includes('plan')) return <DollarSign className="h-4 w-4" />;
    if (lowerKey.includes('user') || lowerKey.includes('admin')) return <Users className="h-4 w-4" />;
    if (lowerKey.includes('database') || lowerKey.includes('table')) return <Database className="h-4 w-4" />;
    if (lowerKey.includes('feature') || lowerKey.includes('flow')) return <Settings className="h-4 w-4" />;
    if (lowerKey.includes('note')) return <FileText className="h-4 w-4" />;
    return <Circle className="h-4 w-4" />;
  };

  const renderSubscriptionPlan = (plan: any) => {
    if (!plan || typeof plan !== 'object') return null;

    return (
      <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg border-l-4 border-l-green-500">
        <div className="flex items-center gap-3 mb-4">
          <DollarSign className="h-6 w-6 text-green-600" />
          <h3 className="text-xl font-bold text-gray-900">{plan.plan_name || 'Subscription Plan'}</h3>
        </div>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <span className="text-2xl font-bold text-green-600">{plan.price}</span>
          </div>
          {plan.features && Array.isArray(plan.features) && (
            <div>
              <h4 className="font-semibold text-gray-700 mb-2">Features:</h4>
              <ul className="space-y-1">
                {plan.features.map((feature: string, index: number) => (
                  <li key={index} className="flex items-center gap-2 text-gray-700">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderAdminPanel = (items: string[]) => {
    return (
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-lg border-l-4 border-l-purple-500">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="h-6 w-6 text-purple-600" />
          <h3 className="text-xl font-bold text-gray-900">Admin Capabilities</h3>
        </div>
        <div className="grid gap-3">
          {items.map((item, index) => (
            <div key={index} className="flex items-start gap-3 p-3 bg-white/60 rounded-lg border border-purple-100">
              <div className="flex-shrink-0 w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mt-0.5">
                {index + 1}
              </div>
              <span className="text-gray-800 leading-relaxed">{item}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const parseTableField = (field: string) => {
    const trimmedField = field.trim();

    // Parse format: "column_name (TYPE, constraints)"
    const match = trimmedField.match(/^([^(]+)\s*\(([^)]+)\)$/);
    if (match) {
      const columnName = match[1].trim();
      const typeAndConstraints = match[2].trim();

      // Split type and constraints
      const parts = typeAndConstraints.split(',').map(p => p.trim());
      const type = parts[0];
      const constraints = parts.slice(1);

      // Check for primary key
      const isPrimary = constraints.some(c => c.toUpperCase() === 'PK');

      // Check for foreign key
      const fkConstraint = constraints.find(c => c.toUpperCase().startsWith('FK TO'));
      const isForeign = !!fkConstraint;
      const fkTarget = fkConstraint ? fkConstraint.replace(/^FK TO\s*/i, '').trim() : undefined;

      return {
        name: columnName,
        type: type.toUpperCase(),
        isPrimary,
        isForeign,
        fkTarget
      };
    }

    // Fallback to old parsing logic for backwards compatibility
    // Handle enum fields with parentheses
    if (trimmedField.includes('(enum:') || trimmedField.includes("(enum: '")) {
      const [name] = trimmedField.split('(enum');
      return { name: name.trim(), type: 'ENUM', isPrimary: false, isForeign: false };
    }

    // Check for foreign key (old format)
    if (trimmedField.includes('(FK')) {
      const [name, fkInfo] = trimmedField.split('(FK');
      const fkTarget = fkInfo.replace(')', '').replace('to ', '').trim();
      return { name: name.trim(), type: 'FOREIGN KEY', isPrimary: false, isForeign: true, fkTarget };
    }

    // Check for primary key (old format)
    if (trimmedField.toLowerCase().includes('id') && !trimmedField.includes('(FK')) {
      return { name: trimmedField, type: 'PRIMARY KEY', isPrimary: true, isForeign: false };
    }

    // Determine field type based on name patterns (old format)
    let type = 'VARCHAR';
    if (trimmedField.includes('_id') || trimmedField.includes('id')) type = 'UUID';
    else if (trimmedField.includes('amount') || trimmedField.includes('count')) type = 'INTEGER';
    else if (trimmedField.includes('date') || trimmedField.includes('_at')) type = 'TIMESTAMP';
    else if (trimmedField.includes('status')) type = 'ENUM';
    else if (trimmedField.includes('text') || trimmedField.includes('summary')) type = 'TEXT';
    else if (trimmedField.includes('path') || trimmedField.includes('name')) type = 'VARCHAR';

    return { name: trimmedField, type, isPrimary: false, isForeign: false };
  };

  const renderDatabaseTables = (tables: any[]) => {
    return (
      <div className="bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50 p-6 rounded-lg border-l-4 border-l-slate-600">
        <div className="flex items-center gap-3 mb-6">
          <Database className="h-7 w-7 text-slate-700" />
          <h3 className="text-2xl font-bold text-gray-900">Database Schema</h3>
          <p>This is for reference only, sometime you may require to modify it or add more</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {tables.map((table, index) => {
            // Handle both old string format and new structured format
            let tableName: string;
            let tableDescription: string;
            let columns: any[];

            if (typeof table === 'string') {
              // Old format: "table_name: column1 (TYPE), column2 (TYPE)"
              const [name, ...descriptionParts] = table.split(':');
              tableName = name.trim();
              const description = descriptionParts.join(':').trim();
              tableDescription = '';

              // Parse fields from old format
              const fields = description
                .split(/,(?![^()]*\))/) // Split by comma but not inside parentheses
                .map(f => f.trim())
                .filter(f => f && f.length > 0);

              columns = fields.map(field => parseTableField(field));
            } else {
              // New structured format
              tableName = table.name;
              tableDescription = table.description;
              columns = table.columns.map((col: any) => ({
                name: col.name,
                type: col.type.toUpperCase(),
                isPrimary: col.constraints?.includes('PK') || false,
                isForeign: col.constraints?.some((c: string) => c.startsWith('FK to')) || false,
                fkTarget: col.constraints?.find((c: string) => c.startsWith('FK to'))?.replace('FK to ', '') || undefined,
                description: col.description
              }));
            }

            return (
              <div key={index} className="bg-white rounded-lg border border-slate-300 shadow-lg overflow-hidden">
                {/* Table Header */}
                <div className="bg-gradient-to-r from-slate-700 to-slate-800 px-6 py-4">
                  <div className="flex items-center gap-3">
                    <Table className="h-6 w-6 text-white" />
                    <div>
                      <h4 className="text-xl font-bold text-white">{tableName.trim()}</h4>
                      {tableDescription && (
                        <p className="text-slate-300 text-sm mt-1">{tableDescription}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Table Structure */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-slate-100 border-b border-slate-200">
                        <th className="px-4 py-3 text-left text-sm font-semibold text-slate-700">Column</th>
                        <th className="px-4 py-3 text-left text-sm font-semibold text-slate-700">Type</th>
                        <th className="px-4 py-3 text-left text-sm font-semibold text-slate-700">Constraints</th>
                      </tr>
                    </thead>
                    <tbody>
                      {columns.map((column, fieldIndex) => {
                        return (
                          <tr key={fieldIndex} className="border-b border-slate-100 hover:bg-slate-50 transition-colors">
                            <td className="px-4 py-3">
                              <div className="flex items-center gap-2">
                                {column.isPrimary && <Key className="h-4 w-4 text-yellow-600" />}
                                {column.isForeign && <Link className="h-4 w-4 text-blue-600" />}
                                <span className="font-mono text-sm text-slate-800">{column.name}</span>
                                {column.description && (
                                  <span className="text-xs text-slate-500 ml-2">({column.description})</span>
                                )}
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              <Badge
                                variant="outline"
                                className={`font-mono text-xs ${
                                  column.type === 'UUID' ? 'border-purple-300 text-purple-700 bg-purple-50' :
                                  column.type === 'TIMESTAMP' ? 'border-green-300 text-green-700 bg-green-50' :
                                  column.type === 'ENUM' ? 'border-orange-300 text-orange-700 bg-orange-50' :
                                  column.type === 'TEXT' ? 'border-indigo-300 text-indigo-700 bg-indigo-50' :
                                  column.type === 'INTEGER' ? 'border-red-300 text-red-700 bg-red-50' :
                                  'border-gray-300 text-gray-700 bg-gray-50'
                                }`}
                              >
                                {column.type}
                              </Badge>
                            </td>
                            <td className="px-4 py-3 text-sm text-slate-600">
                              {column.isPrimary && (
                                <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300 mr-1">PK</Badge>
                              )}
                              {column.isForeign && (
                                <Badge className="bg-blue-100 text-blue-800 border-blue-300 mr-1">
                                  FK → {column.fkTarget}
                                </Badge>
                              )}
                              {!column.isPrimary && !column.isForeign && (
                                <span className="text-slate-500">—</span>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            );
          })}
        </div>

        {/* Schema Legend */}
        <div className="mt-6 p-4 bg-white rounded-lg border border-slate-200">
          <h5 className="text-sm font-semibold text-slate-700 mb-3">Legend:</h5>
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center gap-1">
              <Key className="h-3 w-3 text-yellow-600" />
              <span>Primary Key</span>
            </div>
            <div className="flex items-center gap-1">
              <Link className="h-3 w-3 text-blue-600" />
              <span>Foreign Key</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="outline" className="border-purple-300 text-purple-700 bg-purple-50 text-xs">UUID</Badge>
              <span>Unique Identifier</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="outline" className="border-green-300 text-green-700 bg-green-50 text-xs">TIMESTAMP</Badge>
              <span>Date/Time</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="outline" className="border-orange-300 text-orange-700 bg-orange-50 text-xs">ENUM</Badge>
              <span>Predefined Values</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderMermaidDiagram = (diagramCode: string) => {
    console.log('renderMermaidDiagram called with:', diagramCode.substring(0, 100));

    // Fix diagrams that don't have proper newlines
    let processedDiagram = diagramCode;

    // If the diagram doesn't contain \n but has semicolons, add newlines after semicolons
    if (!diagramCode.includes('\n') && diagramCode.includes(';')) {
      processedDiagram = diagramCode
        .replace(/;\s*/g, ';\n    ') // Add newline and indentation after semicolons
        .replace(/subgraph\s+([^;]+);/g, '\n    subgraph $1\n        ') // Handle subgraph declarations
        .replace(/end;/g, '\n    end\n') // Handle end statements
        .trim();
    }

    console.log('Processed diagram:', processedDiagram.substring(0, 200));
    return <MermaidDiagram diagram={processedDiagram} title="Platform Flow Diagram" />;
  };

  const renderUserFlow = (steps: string[]) => {
    return (
      <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6 rounded-lg border-l-4 border-l-blue-500">
        <div className="flex items-center gap-3 mb-6">
          <Play className="h-6 w-6 text-blue-600" />
          <h3 className="text-xl font-bold text-gray-900">User Journey</h3>
        </div>
        <div className="relative">
          {steps.map((step, index) => (
            <div key={index} className="relative flex items-start gap-4 mb-6 last:mb-0">
              {/* Step Number Circle */}
              <div className="flex-shrink-0 relative">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  {index + 1}
                </div>
                {/* Connecting Line */}
                {index < steps.length - 1 && (
                  <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-0.5 h-6 bg-gradient-to-b from-blue-300 to-indigo-300"></div>
                )}
              </div>

              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <div className="bg-white p-4 rounded-lg shadow-sm border border-blue-100 hover:shadow-md transition-shadow">
                  <p className="text-gray-800 leading-relaxed">{step}</p>
                </div>
              </div>

              {/* Arrow for visual flow */}
              {index < steps.length - 1 && (
                <div className="absolute left-12 top-12 text-blue-300">
                  <ArrowRight className="h-4 w-4" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Flow completion indicator */}
        <div className="mt-6 flex items-center justify-center">
          <div className="flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">User Journey Complete</span>
          </div>
        </div>
      </div>
    );
  };

  const renderAdminFlow = (steps: string[]) => {
    return (
      <div className="bg-gradient-to-br from-purple-50 via-pink-50 to-red-50 p-6 rounded-lg border-l-4 border-l-purple-500">
        <div className="flex items-center gap-3 mb-6">
          <Shield className="h-6 w-6 text-purple-600" />
          <h3 className="text-xl font-bold text-gray-900">Admin Workflow</h3>
        </div>
        <div className="grid gap-4">
          {steps.map((step, index) => (
            <div key={index} className="flex items-center gap-4 p-4 bg-white rounded-lg border border-purple-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                {index + 1}
              </div>
              <p className="text-gray-800 leading-relaxed flex-1">{step}</p>
              {index < steps.length - 1 && (
                <ArrowRight className="h-4 w-4 text-purple-300" />
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderValue = (value: any, key: string): React.ReactNode => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground">—</span>;
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Yes' : 'No'}
        </Badge>
      );
    }

    if (typeof value === 'number') {
      const lowerKey = key.toLowerCase();
      if (lowerKey.includes('price') || lowerKey.includes('cost')) {
        return <span className="font-medium text-green-600">${value}</span>;
      }
      return <span className="font-medium">{value.toLocaleString()}</span>;
    }

    if (typeof value === 'string') {
      // Special handling for Flow diagrams (Mermaid)
      const lowerKey = key.toLowerCase();
      const isFlowDiagramKey = lowerKey.includes('mermaid') || lowerKey.includes('flow') || lowerKey.includes('diagram');

      // Check for various Mermaid diagram types and syntax variations
      const trimmedValue = value.trim();
      const isMermaidContent =
        trimmedValue.includes('flowchart') ||
        trimmedValue.includes('graph TD') ||
        trimmedValue.includes('graph LR') ||
        trimmedValue.includes('graph TB') ||
        trimmedValue.includes('graph BT') ||
        trimmedValue.includes('graph RL') ||
        trimmedValue.includes('sequenceDiagram') ||
        trimmedValue.includes('classDiagram') ||
        trimmedValue.includes('stateDiagram') ||
        trimmedValue.includes('erDiagram') ||
        trimmedValue.includes('journey') ||
        trimmedValue.includes('gantt') ||
        // Handle cases where graph syntax starts with variations
        /^graph\s+(TD|LR|TB|BT|RL)[;\s]/.test(trimmedValue) ||
        /^flowchart\s+(TD|LR|TB|BT|RL)[;\s]/.test(trimmedValue);

      console.log('Checking for Mermaid diagram:', {
        key,
        lowerKey,
        isFlowDiagramKey,
        isMermaidContent,
        valuePreview: trimmedValue.substring(0, 100),
        regexTest: /^graph\s+(TD|LR|TB|BT|RL)[;\s]/.test(trimmedValue)
      });

      if (isFlowDiagramKey && isMermaidContent) {
        console.log('Rendering Mermaid diagram for key:', key);
        return renderMermaidDiagram(value);
      }

      if (value.length > 100) {
        return (
          <div className="text-sm leading-relaxed">
            <p className="whitespace-pre-wrap text-gray-700">{value}</p>
          </div>
        );
      }
      return <span className="text-gray-800">{value}</span>;
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-muted-foreground">None</span>;
      }

      // Check if array contains objects (like user_flow_features)
      if (value.some(item => typeof item === 'object' && item !== null)) {
        // Special handling for user_flow_features with step numbers
        if (key.toLowerCase().includes('flow') || key.toLowerCase().includes('feature')) {
          return (
            <div className="space-y-4">
              {value.map((item, index) => (
                <div key={index} className="border-l-4 border-l-blue-500 pl-6 py-4 bg-blue-50/50 rounded-r-lg">
                  {typeof item === 'object' && item.step_number ? (
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          {item.step_number}
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900">{item.feature_name}</h4>
                      </div>
                      <p className="text-gray-700 leading-relaxed ml-11">{item.description}</p>
                    </div>
                  ) : typeof item === 'object' ? (
                    <FlexibleJsonRenderer data={item} />
                  ) : (
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-500" />
                      <span className="text-gray-800">{String(item)}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          );
        }

        // Check for special array types before default object array rendering
        const lowerKey = key.toLowerCase();

        // Special rendering for database tables
        if (lowerKey.includes('database') && lowerKey.includes('table')) {
          return renderDatabaseTables(value);
        }

        // Special rendering for admin features
        if (lowerKey.includes('admin') && (lowerKey.includes('features') || lowerKey.includes('panel'))) {
          return renderAdminPanel(value);
        }

        // Default object array rendering
        return (
          <div className="space-y-3">
            {value.map((item, index) => (
              <div key={index} className="border-l-4 border-l-blue-200 pl-4 py-2 bg-blue-50/50 rounded-r-lg">
                {typeof item === 'object' ? (
                  <FlexibleJsonRenderer data={item} />
                ) : (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                    <span className="text-gray-800">{String(item)}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      }

      // Simple array of primitives - render as bullet points
      const lowerKey = key.toLowerCase();

      // Special rendering for user journey
      if (lowerKey.includes('user') && (lowerKey.includes('journey') || lowerKey.includes('usage'))) {
        return renderUserFlow(value);
      }

      // Special rendering for admin workflow
      if (lowerKey.includes('admin') && (lowerKey.includes('workflow') || lowerKey.includes('usage'))) {
        return renderAdminFlow(value);
      }

      if (lowerKey.includes('usage') || lowerKey.includes('step') || lowerKey.includes('note')) {
        return (
          <div className="space-y-2">
            {value.map((item, index) => (
              <div key={index} className="flex items-start gap-3 p-2 hover:bg-gray-50 rounded-lg">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                  {index + 1}
                </div>
                <span className="text-gray-700 leading-relaxed">{String(item)}</span>
              </div>
            ))}
          </div>
        );
      }

      // Other arrays as badges
      return (
        <div className="flex flex-wrap gap-2">
          {value.map((item, index) => (
            <Badge key={index} variant="outline" className="text-sm">
              {String(item)}
            </Badge>
          ))}
        </div>
      );
    }

    if (typeof value === 'object') {
      return <FlexibleJsonRenderer data={value} />;
    }

    return <span className="text-gray-800">{String(value)}</span>;
  };

  const entries = Object.entries(data);

  if (entries.length === 0) {
    return null;
  }

  // Extract project description if it exists
  const projectDescription = data.project_description;

  // Filter out project_description and sections now shown in ProjectDescription from main entries
  const sectionsToFilter = [
    'project_description',
    'features',
    'homepage',
    'main_user_features',
    'user_journey',
    'admin_features',
    'admin_workflow',
    'notes',
    'complexity_tier',  // Hide internal complexity tier from users
    'color_code',  // Color scheme is shown separately
    'subscription_plan'  // Now shown next to Exact Features in ProjectDescription
  ];

  const filteredEntries = entries.filter(([key]) => !sectionsToFilter.includes(key));
  const reorderedEntries = filteredEntries.sort(([keyA], [keyB]) => {
    if (keyA.toLowerCase().includes('note')) return 1;
    if (keyB.toLowerCase().includes('note')) return -1;
    return 0;
  });

  return (
    <div className="space-y-6">
      {/* Project Description at the top */}
      {projectDescription && projectName && (
        <ProjectDescription
          data={projectDescription}
          projectName={projectName}
          fullData={data}
          project={project}
        />
      )}

      {title && (
        <>
          <h3 className="text-xl font-bold text-gray-900">{title}</h3>
          <Separator className="my-4" />
        </>
      )}

      <div className="grid gap-6">
        {reorderedEntries.map(([key, value], index) => {
          const formattedKey = formatKey(key);
          const isComplexValue = typeof value === 'object' && value !== null;
          const icon = getIconForKey(key);

          // Special handling for subscription_plan
          if (key === 'subscription_plan' && isComplexValue) {
            return (
              <div key={key} className="mb-6">
                {renderSubscriptionPlan(value)}
              </div>
            );
          }

          if (isComplexValue) {
            return (
              <Card key={key} className="border-l-4 border-l-blue-500 shadow-sm hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    {icon}
                    {formattedKey}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {renderValue(value, key)}
                </CardContent>
              </Card>
            );
          }

          return (
            <div key={key} className="bg-gray-50 p-4 rounded-lg border">
              <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide flex items-center gap-2 mb-2">
                {icon}
                {formattedKey}
              </label>
              <div className="text-base">
                {renderValue(value, key)}
              </div>
            </div>
          );
        })}
      </div>

      {/* Definition of Done - Always shown at the end */}
      <div className="mt-8">
        <Card className="border-green-200 bg-green-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              Definition of Done
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-green-700 mb-4">
              A project is considered <strong>done</strong> when all of the following are completed:
            </p>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">All required features are working as expected (minor bugs/errors in console are acceptable, but the flow must work)</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">The app is fully integrated with Supabase (auth, database, policies, storage if needed).</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">The user dashboard and admin panel are both functional.</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">Homepage includes all sections: Header, Hero, Content, Footer.</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">Stripe is set up with at least one working pricing plan (test mode is okay).</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">User can sign up, log in and manage their profile.</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">Admin can manage users and assign plans/packages.</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">The project runs without critical errors or broken flows.</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">You have used consistent header/footer across all pages.</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">The site must include meaningful dummy data and content, do not leave pages empty or without example data.</span>
              </div>
            </div>

          </CardContent>
        </Card>
      </div>
    </div>
  );
}


