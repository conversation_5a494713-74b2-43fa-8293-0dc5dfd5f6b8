import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Supabase configuration
const SUPABASE_URL = "https://xsiwdckklpslfxppdjhu.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhzaXdkY2trbHBzbGZ4cHBkamh1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyODE3OTgsImV4cCI6MjA2ODg1Nzc5OH0.JAhwChLZFHip_31t73thqAwg4-MrsfHS56X8B-pR9hQ";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Default content structure for projects
const defaultProjectContent = {
  description: '',
  features: [],
  requirements: [],
  tech_stack: [],
  difficulty: 'Medium',
  estimated_hours: 40,
  additional_notes: ''
};

function parseMarkdownTable(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    // Find the table header line
    const headerLineIndex = lines.findIndex(line =>
      line.includes('SaaS Idea Name') &&
      line.includes('Description') &&
      line.includes('Category')
    );

    if (headerLineIndex === -1) {
      throw new Error('Could not find table header in markdown file');
    }

    console.log(`Found table header at line ${headerLineIndex + 1}`);

    // Skip header and separator lines
    const dataLines = lines.slice(headerLineIndex + 2);

    const projects = [];
    let lineNumber = headerLineIndex + 3; // Start counting from first data line

    for (const line of dataLines) {
      // Skip empty lines and lines that don't look like table rows
      if (!line.trim() || !line.includes('|')) {
        lineNumber++;
        continue;
      }

      // Parse the table row - split by | and clean up
      const columns = line.split('|').map(col => col.trim());

      // Remove empty first and last elements (from leading/trailing |)
      if (columns[0] === '') columns.shift();
      if (columns[columns.length - 1] === '') columns.pop();

      // Debug: log the first few rows
      if (projects.length < 3) {
        console.log(`Line ${lineNumber}: Found ${columns.length} columns:`, columns);
      }

      // Ensure we have at least 6 columns (name, description, category, target_user, status, claimed_by)
      if (columns.length >= 6) {
        const [name, description, category, target_user, status, claimed_by] = columns;

        // Skip if this looks like a separator row
        if (name.includes('---') || description.includes('---')) {
          lineNumber++;
          continue;
        }

        // Clean up claimed_by field (empty string becomes null)
        const cleanedClaimedBy = claimed_by && claimed_by.trim() !== '' ? claimed_by.trim() : null;

        projects.push({
          name: name.trim(),
          description: description.trim(),
          category: category.trim(),
          target_user: target_user.trim(),
          status: status.trim() || 'Unclaimed',
          claimed_by: cleanedClaimedBy,
          content: {
            ...defaultProjectContent,
            description: description.trim()
          }
        });
      }

      lineNumber++;
    }

    return projects;
  } catch (error) {
    console.error('Error parsing markdown file:', error);
    throw error;
  }
}

async function clearExistingProjects() {
  console.log('Clearing existing projects...');
  const { error } = await supabase
    .from('projects')
    .delete()
    .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all projects
  
  if (error) {
    console.error('Error clearing projects:', error);
    throw error;
  }
  
  console.log('Existing projects cleared successfully');
}

async function insertProjects(projects) {
  console.log(`Inserting ${projects.length} projects...`);
  
  // Insert projects in batches to avoid hitting limits
  const batchSize = 50;
  let insertedCount = 0;
  
  for (let i = 0; i < projects.length; i += batchSize) {
    const batch = projects.slice(i, i + batchSize);
    
    const { data, error } = await supabase
      .from('projects')
      .insert(batch)
      .select();
    
    if (error) {
      console.error(`Error inserting batch ${Math.floor(i / batchSize) + 1}:`, error);
      throw error;
    }
    
    insertedCount += batch.length;
    console.log(`Inserted batch ${Math.floor(i / batchSize) + 1}: ${batch.length} projects (Total: ${insertedCount})`);
  }
  
  return insertedCount;
}

async function main() {
  try {
    console.log('Starting database population...');
    
    // Parse the markdown file
    const markdownPath = path.join(process.cwd(), 'docs', 'projects.md');
    console.log(`Reading projects from: ${markdownPath}`);
    
    const projects = parseMarkdownTable(markdownPath);
    console.log(`Parsed ${projects.length} projects from markdown file`);
    
    // Show first few projects for verification
    console.log('\nFirst 3 projects:');
    projects.slice(0, 3).forEach((project, index) => {
      console.log(`${index + 1}. ${project.name} - ${project.category} - ${project.target_user}`);
    });
    
    // Clear existing projects (optional - comment out if you want to keep existing data)
    await clearExistingProjects();
    
    // Insert new projects
    const insertedCount = await insertProjects(projects);
    
    console.log(`\n✅ Successfully populated database with ${insertedCount} projects!`);
    
  } catch (error) {
    console.error('❌ Error populating database:', error);
    process.exit(1);
  }
}

// Run the script
main();
