import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { AlertTriangle, Sparkles, Loader2, RefreshCw, Plus } from 'lucide-react';

interface CriticalNotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (criticalNotes: string) => void;
  onEnhance: (enhancementRequests: string) => void;
  projectName: string;
  isGenerating: boolean;
  hasExistingContent: boolean;
}

export function CriticalNotesModal({
  isOpen,
  onClose,
  onGenerate,
  onEnhance,
  projectName,
  isGenerating,
  hasExistingContent
}: CriticalNotesModalProps) {
  const [criticalNotes, setCriticalNotes] = useState('');
  const [enhancementRequests, setEnhancementRequests] = useState('');
  const [mode, setMode] = useState<'regenerate' | 'enhance'>('regenerate');

  const handleGenerate = () => {
    if (mode === 'enhance') {
      onEnhance(enhancementRequests);
      setEnhancementRequests(''); // Clear the enhancement requests after generation
    } else {
      onGenerate(criticalNotes);
      setCriticalNotes(''); // Clear the notes after generation
    }
  };

  const handleClose = () => {
    if (!isGenerating) {
      setCriticalNotes('');
      setEnhancementRequests('');
      setMode('regenerate');
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Critical Notes for "{projectName}"
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Mode Selection - Only show if project has existing content */}
          {hasExistingContent && (
            <div className="space-y-3">
              <Label className="text-base font-medium">Generation Mode</Label>
              <RadioGroup value={mode} onValueChange={(value) => setMode(value as 'regenerate' | 'enhance')}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="regenerate" id="regenerate" />
                  <Label htmlFor="regenerate" className="flex items-center gap-2 cursor-pointer">
                    <RefreshCw className="h-4 w-4" />
                    Re-generate New MVP (Start fresh with new content)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="enhance" id="enhance" />
                  <Label htmlFor="enhance" className="flex items-center gap-2 cursor-pointer">
                    <Plus className="h-4 w-4" />
                    Enhance Existing MVP (Add features to current content)
                  </Label>
                </div>
              </RadioGroup>
            </div>
          )}

          <div className={`border rounded-lg p-4 ${mode === 'enhance' ? 'bg-blue-50 border-blue-200' : 'bg-orange-50 border-orange-200'}`}>
            <div className="flex items-start gap-2">
              {mode === 'enhance' ? (
                <Plus className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
              )}
              <div className={`text-sm ${mode === 'enhance' ? 'text-blue-800' : 'text-orange-800'}`}>
                <p className="font-medium mb-1">
                  {mode === 'enhance' ? 'Enhancement Instructions' : 'Important Instructions'}
                </p>
                <p>
                  {mode === 'enhance'
                    ? 'Describe what features or improvements you want to add to the existing MVP. The AI will enhance the current content while preserving existing functionality.'
                    : 'These critical notes will be passed to the AI as the most important requirements. They will take priority over standard requirements and must be incorporated into the MVP design.'
                  }
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor={mode === 'enhance' ? 'enhancement-requests' : 'critical-notes'}>
              {mode === 'enhance' ? 'Enhancement Requests (Optional)' : 'Critical Notes (Optional)'}
            </Label>
            {mode === 'enhance' ? (
              <Textarea
                id="enhancement-requests"
                placeholder="Describe what features or improvements you want to add to the existing MVP. For example:
- Add new user roles (admin, manager, viewer)
- Integrate with additional payment providers (PayPal, Apple Pay)
- Add reporting and analytics dashboard
- Implement real-time notifications
- Add mobile app support
- Include advanced search and filtering
- Add API integrations (Slack, Discord, etc.)
- Implement advanced security features"
                value={enhancementRequests}
                onChange={(e) => setEnhancementRequests(e.target.value)}
                className="min-h-[120px] resize-none"
                disabled={isGenerating}
              />
            ) : (
              <Textarea
                id="critical-notes"
                placeholder="Enter any critical requirements, constraints, or specific features that must be included in the MVP generation. For example:
- Must include specific integrations (e.g., Stripe, PayPal, specific APIs)
- Required compliance standards (GDPR, HIPAA, etc.)
- Specific technology requirements
- Unique business logic or workflows
- Critical user experience requirements"
                value={criticalNotes}
                onChange={(e) => setCriticalNotes(e.target.value)}
                className="min-h-[120px] resize-none"
                disabled={isGenerating}
              />
            )}
            <p className="text-xs text-gray-500">
              {mode === 'enhance'
                ? 'Leave empty to enhance the existing MVP with general improvements.'
                : 'Leave empty to use standard MVP generation without special requirements.'
              }
            </p>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isGenerating}
          >
            Cancel
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="min-w-[140px]"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {mode === 'enhance' ? 'Enhancing...' : 'Generating...'}
              </>
            ) : (
              <>
                {mode === 'enhance' ? (
                  <Plus className="h-4 w-4 mr-2" />
                ) : (
                  <Sparkles className="h-4 w-4 mr-2" />
                )}
                {mode === 'enhance' ? 'Enhance MVP' : 'Generate MVP'}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
