import { supabase } from '@/integrations/supabase/client';

export interface Prompt {
  id: string;
  name: string;
  type: 'system' | 'user';
  content: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreatePromptData {
  name: string;
  type: 'system' | 'user';
  content: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdatePromptData {
  name?: string;
  type?: 'system' | 'user';
  content?: string;
  description?: string;
  is_active?: boolean;
}

class PromptService {
  async getAllPrompts(): Promise<Prompt[]> {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch prompts: ${error.message}`);
    }

    return data || [];
  }

  async getActivePrompts(): Promise<Prompt[]> {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch active prompts: ${error.message}`);
    }

    return data || [];
  }

  async getPromptByName(name: string): Promise<Prompt | null> {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('name', name)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows found
      }
      throw new Error(`Failed to fetch prompt: ${error.message}`);
    }

    return data;
  }

  async getPromptsByType(type: 'system' | 'user'): Promise<Prompt[]> {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('type', type)
      .eq('is_active', true)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch prompts by type: ${error.message}`);
    }

    return data || [];
  }

  async createPrompt(promptData: CreatePromptData): Promise<Prompt> {
    const { data, error } = await supabase
      .from('prompts')
      .insert([promptData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create prompt: ${error.message}`);
    }

    return data;
  }

  async updatePrompt(id: string, promptData: UpdatePromptData): Promise<Prompt> {
    const { data, error } = await supabase
      .from('prompts')
      .update(promptData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update prompt: ${error.message}`);
    }

    return data;
  }

  async deletePrompt(id: string): Promise<void> {
    const { error } = await supabase
      .from('prompts')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete prompt: ${error.message}`);
    }
  }

  // Specific methods for idea generation prompts
  async getIdeaGenerationSystemPrompt(): Promise<string> {
    const prompt = await this.getPromptByName('idea_generation_system');
    if (!prompt || !prompt.is_active) {
      throw new Error('System prompt for idea generation not found or inactive');
    }
    return prompt.content;
  }

  async getIdeaGenerationUserPrompt(): Promise<string> {
    const prompt = await this.getPromptByName('idea_generation_user');
    if (!prompt || !prompt.is_active) {
      throw new Error('User prompt for idea generation not found or inactive');
    }
    return prompt.content;
  }
}

export const promptService = new PromptService();
