-- Create projects table
CREATE TABLE public.projects (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  category TEXT NOT NULL,
  target_user TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'Unclaimed',
  claimed_by TEXT,
  content TEXT, -- Additional details for modal
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for now (since this is internal)
CREATE POLICY "Allow all operations on projects" 
ON public.projects 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_projects_updated_at
  BEFORE UPDATE ON public.projects
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Insert sample data
INSERT INTO public.projects (name, description, category, target_user, status, claimed_by, content) VALUES
('AI Meeting Summarizer', 'Records meetings, summarizes, assigns action items', 'Productivity', 'Remote Teams', 'Unclaimed', NULL, 'A comprehensive tool that integrates with popular video conferencing platforms to automatically record meetings, extract key points, and generate actionable summaries with assigned responsibilities.'),
('Time-Zone Coordination Tool', 'Helps global teams schedule meetings', 'Productivity', 'Remote Teams', 'Unclaimed', NULL, 'An intelligent scheduling assistant that automatically finds optimal meeting times across multiple time zones, considering team members'' working hours and preferences.');