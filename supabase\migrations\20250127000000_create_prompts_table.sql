-- Create prompts table for storing editable AI prompts
CREATE TABLE public.prompts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  type TEXT NOT NULL CHECK (type IN ('system', 'user')),
  content TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.prompts ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for now (since this is internal)
CREATE POLICY "Allow all operations on prompts" 
ON public.prompts 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_prompts_updated_at
  BEFORE UPDATE ON public.prompts
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default prompts
INSERT INTO public.prompts (name, type, content, description) VALUES
(
  'idea_generation_system',
  'system',
  'You are an expert SaaS idea generator specializing in simple, easy-to-build platforms that provide real value to users.

Your task is to generate exactly 10 unique SaaS ideas that are:
- Simple to build and maintain
- Have clear value propositions
- Focus on template-based or AI-powered solutions
- Target specific user groups with real problems
- Avoid complex integrations or advanced features

Guidelines:
- DO NOT use words like "generator", "builder", "template", "kit" in the names
- Focus on the end result/benefit, not the tool itself
- Each SaaS should be simple: users pay → access features → get value
- Think template-based platforms, AI evaluation tools, marketplace platforms
- Avoid: branding tools, email tools, HTML/UI tools, PR tools, generic templates

Examples of good simple SaaS:
- Invoice platforms with predefined templates
- AI-powered evaluation platforms
- Simple marketplace platforms
- Subscription-based access to curated resources

You must respond with exactly 10 ideas in the specified JSON schema format.',
  'System instruction for AI idea generation'
),
(
  'idea_generation_user',
  'user',
  'I need saas that are easy to build, dont mention the word generator, builder as those naming suck. But when i mean easy to build, it should be a simple like:

- Write Invoice Platform - Create invoices, manage clients, track tasks, and run your entire business from one clean, powerful platform.
This write invoice platform simply as a section for invoice where people we have a set of predefined templates and the user simply need to choose from it and click on generate to download. So the idea here is, its a simple SaaS where people just pay a package and then can access templates to use. So as admin, we simply add templates for user to use. Simple as that.

- Expert-Recruiters - This is a ai cv evaluation platform where agency register, and upload bulk cvs for analysis, and it get evalated by a set of companies he is managing automatically. The user has a specific /dashboard for that. There are 3 packages where user simply select and based on the subscription plan, he can uploaded a specific numbers of cvs monthly and bulk analyse and evalation by AI.

- EpicFreelancers - This is a platform where user register as seller or buyer to buy diffferent services. The category is simple, we have service, saas, digital product. Here its simple because as a seller, they have a dashboard where they simple choose their category and what they are selling or offering and the design changes accordingly in front-end to display, simple as that.

So basically the idea here is to give me a list of similar projects that i can develop.

And please dont give app like Saas template, branding kits, email tools, html/ui tools, PR tool.

Please respond with a valid JSON object that follows this EXACT structure:

{
  "ideas": [
    {
      "title": "SaaS system idea Name, give a meaning full title please",
      "summary": "Brief 5-10 word summary for display",
      "description": "Brief description of what the platform does and its value proposition",
      "category": "Business category (e.g., Finance, HR, Marketplace, etc.)",
      "target_user": "Who would use this platform (e.g., Small Businesses, Agencies, Freelancers, etc.)"
    }
  ]
}

Generate exactly 10 unique, simple, and valuable SaaS ideas.',
  'User prompt template for AI idea generation'
);
