import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { AlertTriangle, Lightbulb, Loader2 } from 'lucide-react';

interface IdeaCriticalNotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (criticalNotes?: string) => void;
  isGenerating: boolean;
}

export function IdeaCriticalNotesModal({
  isOpen,
  onClose,
  onGenerate,
  isGenerating
}: IdeaCriticalNotesModalProps) {
  const [criticalNotes, setCriticalNotes] = useState('');

  const handleGenerate = () => {
    onGenerate(criticalNotes.trim() || undefined);
    setCriticalNotes(''); // Clear the notes after generation
  };

  const handleClose = () => {
    if (!isGenerating) {
      setCriticalNotes('');
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Critical Requirements for Idea Generation
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="border rounded-lg p-4 bg-orange-50 border-orange-200">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-orange-800">
                <p className="font-medium mb-1">High Priority Instructions</p>
                <p>
                  These critical notes will be passed to the AI as the most important requirements. 
                  They will take priority over standard requirements and must be incorporated into 
                  all generated SaaS ideas.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="critical-notes">
              Critical Requirements (Optional)
            </Label>
            <Textarea
              id="critical-notes"
              placeholder="Enter any critical requirements, constraints, or specific focus areas for the SaaS ideas. For example:
- Must focus on specific industries (healthcare, finance, education, etc.)
- Required technology stack or integrations
- Target specific user types (small businesses, enterprises, freelancers, etc.)
- Compliance requirements (GDPR, HIPAA, SOX, etc.)
- Specific business models (subscription, marketplace, freemium, etc.)
- Geographic or market constraints
- Innovation focus areas (AI/ML, blockchain, IoT, etc.)"
              value={criticalNotes}
              onChange={(e) => setCriticalNotes(e.target.value)}
              className="min-h-[120px] resize-none"
              disabled={isGenerating}
            />
            <p className="text-xs text-gray-500">
              Leave empty to use standard idea generation without special requirements.
            </p>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isGenerating}
          >
            Cancel
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="min-w-[140px]"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Lightbulb className="h-4 w-4 mr-2" />
                Generate Ideas
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
