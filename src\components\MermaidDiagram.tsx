import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Copy, ExternalLink, Eye, Code } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface MermaidDiagramProps {
  diagram: string;
  title?: string;
}

export function MermaidDiagram({ diagram, title = "Mermaid Diagram" }: MermaidDiagramProps) {
  const diagramRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCode, setShowCode] = useState(false);
  const renderingRef = useRef(false);

  useEffect(() => {
    const renderDiagram = async () => {
      // Prevent multiple simultaneous renders
      if (renderingRef.current) return;

      try {
        renderingRef.current = true;
        setIsLoading(true);
        setError(null);

        // Check if diagram is empty or invalid
        if (!diagram || typeof diagram !== 'string' || diagram.trim().length === 0) {
          setError('No diagram data provided');
          return;
        }

        // Wait for container to be available (with timeout)
        let attempts = 0;
        const maxAttempts = 10;
        while (!diagramRef.current && attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 100));
          attempts++;
        }

        // Check if container exists after waiting
        if (!diagramRef.current) {
          setError('Diagram container not found');
          return;
        }

        console.log('Starting to render diagram...');

        // Dynamically import mermaid
        const mermaidModule = await import('mermaid');
        const mermaid = mermaidModule.default;

        // Initialize mermaid with configuration
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'Inter, system-ui, sans-serif',
          fontSize: 14,
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis',
            padding: 20,
          },
          themeVariables: {
            primaryColor: '#3b82f6',
            primaryTextColor: '#1f2937',
            primaryBorderColor: '#2563eb',
            lineColor: '#6b7280',
            secondaryColor: '#f3f4f6',
            tertiaryColor: '#f9fafb',
            background: '#ffffff',
            mainBkg: '#ffffff',
            secondBkg: '#f8fafc',
            tertiaryBkg: '#f1f5f9',
          }
        });

        // Validate diagram syntax before rendering
        try {
          await mermaid.parse(diagram);
        } catch (parseError) {
          console.error('Mermaid parse error:', parseError);
          setError(`Invalid diagram syntax: ${parseError instanceof Error ? parseError.message : 'Unknown parse error'}`);
          return;
        }

        // Clear previous content
        diagramRef.current.innerHTML = '';

        // Generate unique ID for this diagram
        const diagramId = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        // Render the diagram
        const { svg } = await mermaid.render(diagramId, diagram);

        console.log('Mermaid render successful, SVG length:', svg.length);

        // Insert the SVG into the container
        if (diagramRef.current) {
          diagramRef.current.innerHTML = svg;

          // Style the SVG for better appearance
          const svgElement = diagramRef.current.querySelector('svg');
          if (svgElement) {
            svgElement.style.maxWidth = '100%';
            svgElement.style.height = 'auto';
            svgElement.style.background = 'white';
            svgElement.style.borderRadius = '8px';
            svgElement.style.padding = '20px';
            svgElement.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';
          }
        }
      } catch (err) {
        console.error('Error rendering Mermaid diagram:', err);
        setError(`Failed to render diagram: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setIsLoading(false);
        renderingRef.current = false;
      }
    };

    if (diagram && diagram.trim().length > 0) {
      renderDiagram();
    } else {
      setIsLoading(false);
      setError('No diagram data provided');
    }
  }, [diagram]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(diagram);
      toast({
        title: "Copied!",
        description: "Mermaid diagram code copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const openInMermaidLive = () => {
    try {
      // Use the correct Mermaid Live Editor URL format
      // The new format uses base64url encoding without padding
      const base64 = btoa(diagram)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

      const url = `https://mermaid.live/edit#base64:${base64}`;
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error opening Mermaid Live Editor:', error);
      toast({
        title: "Error",
        description: "Failed to open Mermaid Live Editor",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 p-6 rounded-lg border-l-4 border-l-indigo-500">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-indigo-100 rounded-lg">
            <Eye className="h-5 w-5 text-indigo-600" />
          </div>
          <h3 className="text-xl font-bold text-gray-900">{title}</h3>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCode(!showCode)}
            className="text-xs"
          >
            <Code className="h-3 w-3 mr-1" />
            {showCode ? 'Hide Code' : 'Show Code'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={copyToClipboard}
            className="text-xs"
          >
            <Copy className="h-3 w-3 mr-1" />
            Copy
          </Button>
          {/* <Button
            variant="outline"
            size="sm"
            onClick={openInMermaidLive}
            className="text-xs"
          >
            <ExternalLink className="h-3 w-3 mr-1" />
            Live Editor
          </Button> */}
        </div>
      </div>

      {showCode && (
        <div className="mb-6 bg-white p-4 rounded-lg border border-indigo-200">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">Mermaid Code:</h4>
          <pre className="text-xs bg-gray-50 p-3 rounded border overflow-x-auto">
            <code>{diagram}</code>
          </pre>
        </div>
      )}

      <div className="bg-white rounded-lg border border-indigo-200 shadow-sm overflow-hidden">
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span className="ml-3 text-gray-600">Rendering diagram...</span>
          </div>
        )}
        
        {error && (
          <div className="p-6 text-center">
            <div className="text-red-600 mb-2">⚠️ Diagram Render Error</div>
            <p className="text-sm text-gray-600 mb-4">{error}</p>
            <div className="bg-gray-50 p-3 rounded text-xs text-left">
              <pre>{diagram}</pre>
            </div>
          </div>
        )}
        
        <div className="p-4">
          <div
            ref={diagramRef}
            className="flex justify-center items-center min-h-[200px]"
            style={{ display: isLoading || error ? 'none' : 'block' }}
          />
        </div>
      </div>

      
    </div>
  );
}
