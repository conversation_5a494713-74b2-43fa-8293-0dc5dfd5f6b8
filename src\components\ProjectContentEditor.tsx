import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';
import { ProjectContent, normalizeProjectContent, defaultProjectContent } from '@/types/project';

interface ProjectContentEditorProps {
  content: ProjectContent | null;
  onChange: (content: ProjectContent) => void;
}

export function ProjectContentEditor({ content, onChange }: ProjectContentEditorProps) {
  const [localContent, setLocalContent] = useState<ProjectContent>(
    normalizeProjectContent(content)
  );

  useEffect(() => {
    setLocalContent(normalizeProjectContent(content));
  }, [content]);

  const updateContent = (updates: Partial<ProjectContent>) => {
    const newContent = { ...localContent, ...updates };
    setLocalContent(newContent);
    onChange(newContent);
  };

  const addArrayItem = (field: 'features' | 'requirements' | 'tech_stack', value: string) => {
    if (value.trim()) {
      const currentArray = localContent[field] || [];
      updateContent({
        [field]: [...currentArray, value.trim()]
      });
    }
  };

  const removeArrayItem = (field: 'features' | 'requirements' | 'tech_stack', index: number) => {
    const currentArray = localContent[field] || [];
    updateContent({
      [field]: currentArray.filter((_, i) => i !== index)
    });
  };

  const ArrayEditor = ({ 
    field, 
    title, 
    placeholder 
  }: { 
    field: 'features' | 'requirements' | 'tech_stack';
    title: string;
    placeholder: string;
  }) => {
    const [inputValue, setInputValue] = useState('');
    const items = localContent[field] || [];

    const handleAdd = () => {
      addArrayItem(field, inputValue);
      setInputValue('');
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleAdd();
      }
    };

    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex gap-2">
            <Input
              placeholder={placeholder}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
            />
            <Button onClick={handleAdd} size="sm" disabled={!inputValue.trim()}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {items.map((item, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {item}
                <X 
                  className="h-3 w-3 cursor-pointer hover:text-destructive" 
                  onClick={() => removeArrayItem(field, index)}
                />
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="text-sm font-medium">Description</label>
        <Textarea
          placeholder="Detailed project description..."
          value={localContent.description || ''}
          onChange={(e) => updateContent({ description: e.target.value })}
          className="mt-1"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium">Difficulty</label>
          <Select 
            value={localContent.difficulty || 'Medium'} 
            onValueChange={(value: 'Easy' | 'Medium' | 'Hard') => updateContent({ difficulty: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Easy">Easy</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="Hard">Hard</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium">Estimated Hours</label>
          <Input
            type="number"
            placeholder="40"
            value={localContent.estimated_hours || ''}
            onChange={(e) => updateContent({ estimated_hours: parseInt(e.target.value) || 0 })}
            className="mt-1"
          />
        </div>
      </div>

      <ArrayEditor 
        field="features" 
        title="Key Features" 
        placeholder="Add a feature..." 
      />

      <ArrayEditor 
        field="requirements" 
        title="Requirements" 
        placeholder="Add a requirement..." 
      />

      <ArrayEditor 
        field="tech_stack" 
        title="Technology Stack" 
        placeholder="Add a technology..." 
      />

      <div>
        <label className="text-sm font-medium">Additional Notes</label>
        <Textarea
          placeholder="Any additional notes or considerations..."
          value={localContent.additional_notes || ''}
          onChange={(e) => updateContent({ additional_notes: e.target.value })}
          className="mt-1"
        />
      </div>
    </div>
  );
}
