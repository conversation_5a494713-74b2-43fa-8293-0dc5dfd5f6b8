import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON><PERSON>, DialogTrigger } from '@/components/ui/dialog';
import { Lightbulb, Target, CheckCircle, DollarSign, BookOpen, ExternalLink, Copy, Check } from 'lucide-react';
import { ColorSchemeDisplay } from '@/components/ColorSchemeDisplay';
import { toast } from '@/hooks/use-toast';

interface ProjectDescriptionData {
  overview: string;
  problem_statement: string;
  solution: string;
}

interface ProjectDescriptionProps {
  data: ProjectDescriptionData;
  projectName: string;
  fullData?: any; // Full project data to access other sections
  project?: any; // Full project object to access landing_page_image
}

export function ProjectDescription({ data, projectName, fullData, project }: ProjectDescriptionProps) {
  const [copied, setCopied] = useState(false);

  const generatePromptContent = () => {
    // Create a comprehensive prompt with all project data, excluding unwanted fields
    const filteredData = { ...fullData };

    // Remove unwanted fields
    delete filteredData.complexity_tier;
    delete filteredData.database_tables;

    // Include color scheme from project if available
    const projectDataWithColors = {
      name: projectName,
      description: data.overview,
      ...filteredData,
      ...(project?.color_code && { color_scheme: project.color_code })
    };

    const projectData = JSON.stringify(projectDataWithColors, null, 2);

    return `build the below and make sure its a full detailed landing page with maximum details in the landing page, add a lot of details in the landing page, add the price subscription in the landing page etc but no need to use supabase and login system for now, but do create the page without real supabase implementation. And please make sure to use less icon, emoji in the ui, please use real image, i want everything to use real images in the UI. develop its frontend and backend and all nessary page please. Do it all! do not stop! Follow the exact below context, do not add more, do not add less. Follow exact features. Use fewer icons and avoid the typical AI-style design, try to make the UI look unique. Make the hero relates to the SaaS we are building. Do not any icon/emoji on the main hero section and make the background to use real image instead of a grandient color(make the hero look super powerfull). Use only 1 login page for both login/signup and it must use a 2 column where on the left you write some content, instruction, and use real image in the background and while on the right side, you add the login/signup stuff. Again, full focus on the below details!

Clone:
${projectData}`;
  };

  const copyToClipboard = async () => {
    try {
      const promptContent = generatePromptContent();
      await navigator.clipboard.writeText(promptContent);
      setCopied(true);
      toast({
        title: "Copied!",
        description: "Prompt copied to clipboard successfully",
      });

      // Reset the copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy prompt to clipboard",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8 rounded-xl border border-blue-200 shadow-lg mb-8">
      {/* Tips Link */}
      <div className="text-center mb-6">
        <Button
          onClick={() => window.open('/private-tips', '_blank')}
          className="bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 text-white font-semibold px-6 py-2 rounded-full shadow-lg hover:shadow-purple-500/25 transform hover:scale-105 transition-all duration-300 border border-white/20"
        >
          <BookOpen className="h-4 w-4 mr-2" />
          See tips how to complete this project faster
          <ExternalLink className="h-4 w-4 ml-2" />
        </Button>
      </div>

      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-3">{projectName}</h1>
        <p className="text-lg text-gray-700 leading-relaxed max-w-4xl mx-auto">
          {data.overview}
        </p>
      </div>

      {/* Problem Statement and Solution sections hidden as requested */}

      {/* Features Section */}
      {fullData?.features && Array.isArray(fullData.features) && fullData.features.length > 0 && (
        <div className="mb-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Exact Features */}
            <Card className="border-blue-200 bg-blue-50/50">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold text-blue-800 flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Exact Features
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {fullData.features.map((feature: string, index: number) => (
                    <li key={index} className="flex items-start gap-2 text-gray-700">
                      <span className="text-blue-600 mt-1">•</span>
                      <span className="leading-relaxed">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Subscription Plan */}
            {fullData?.subscription_plan && (
              <Card className="border-green-200 bg-green-50/50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-green-800 flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    {fullData.subscription_plan.plan_name || 'Subscription Plan'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl font-bold text-green-600">{fullData.subscription_plan.price}</span>
                    </div>
                    {fullData.subscription_plan.features && Array.isArray(fullData.subscription_plan.features) && (
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Features:</h4>
                        <ul className="space-y-1">
                          {fullData.subscription_plan.features.map((feature: string, index: number) => (
                            <li key={index} className="flex items-start gap-2 text-gray-700">
                              <span className="text-green-600 mt-1">•</span>
                              <span className="text-sm leading-relaxed">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Implementation Note */}
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <strong>📝 Implementation Note:</strong> This is the only subscription plan you need to implement for this project.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* Additional Sections in Plain Text */}
      {fullData && (
        <div className="mt-8 space-y-6 bg-white/60 p-6 rounded-lg border border-blue-200">
          {/* Homepage */}
          {fullData.homepage && Array.isArray(fullData.homepage) && (
            <div>
              <div className="flex items-center justify-center gap-4 mb-3">
                <h2 className="text-lg font-semibold text-gray-900">Homepage</h2>

                {/* Copy Prompt Button */}
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700 hover:text-green-800 font-medium"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy Prompt
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <div className="space-y-2">
                        <DialogTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
                          <Copy className="h-5 w-5 text-green-600" />
                          Copy Development Prompt
                        </DialogTitle>
                        <p className="text-sm text-gray-600">
                          <Button
                            variant="link"
                            className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium text-sm"
                            onClick={() => window.open('/private-tips', '_blank')}
                          >
                            See how to use the prompt and on which platform to use it to build the UI
                          </Button>
                        </p>
                      </div>
                    </DialogHeader>
                    <div className="mt-4">
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <div className="flex items-center justify-between mb-3">
                          <p className="text-sm text-gray-600 font-medium">
                            Complete development prompt with project details:
                          </p>
                          <Button
                            onClick={copyToClipboard}
                            size="sm"
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            {copied ? (
                              <>
                                <Check className="h-4 w-4 mr-2" />
                                Copied!
                              </>
                            ) : (
                              <>
                                <Copy className="h-4 w-4 mr-2" />
                                Copy to Clipboard
                              </>
                            )}
                          </Button>
                        </div>
                        <div className="bg-white p-4 rounded border border-gray-300 max-h-96 overflow-y-auto">
                          <pre className="text-sm text-gray-800 whitespace-pre-wrap font-mono">
                            {generatePromptContent()}
                          </pre>
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {/* Landing Page Image */}
              {/* {project?.landing_page_image && (
                <div className="mb-6">
                  <div className="text-center mb-3">
                    <h3 className="text-md font-medium text-gray-800 mb-1">Landing Page Sample</h3>
                    <p className="text-sm text-gray-600">
                      Generate it using Lovable.dev
                    </p>
                  </div>
                  <div className="border rounded-lg overflow-hidden bg-gray-50 shadow-md">
                    <img
                      src={project.landing_page_image}
                      alt={`Landing page sample for ${projectName}`}
                      className="w-full h-auto object-contain max-h-[600px]"
                      onError={(e) => {
                        console.error('Failed to load landing page image:', project.landing_page_image);
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                </div>
              )} */}

              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h3 className="text-md font-medium text-gray-800 mb-3">Homepage Structure</h3>
                <ul className="list-disc list-inside space-y-2 text-gray-700">
                  {fullData.homepage.map((section: any, index: number) => (
                    <li key={index} className="leading-relaxed">
                      <strong>{section.section_name}:</strong> {section.description}
                      {section.section_name === 'Hero Section' && (
                        <span className="font-bold text-red-600">{/* "or follow the screenshot" */}</span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Two-column layout for User vs Admin sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* User Side */}
            <div className="space-y-6">
              <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-l-blue-500">
                <h2 className="text-xl font-bold text-blue-800 mb-4 text-center">👤 User Experience</h2>

                {/* Main Features for User */}
                {fullData.main_user_features && Array.isArray(fullData.main_user_features) && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Main Features for User</h3>
                    <ol className="list-decimal list-inside space-y-2 text-gray-700">
                      {fullData.main_user_features.map((feature: any, index: number) => (
                        <li key={index} className="leading-relaxed">
                          <strong>{feature.feature_name || `Step ${feature.step_number || index + 1}`}:</strong> {feature.description}
                        </li>
                      ))}
                    </ol>
                  </div>
                )}

                {/* User Flow */}
                {fullData.user_journey && Array.isArray(fullData.user_journey) && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">User Flow</h3>
                    <ol className="list-decimal list-inside space-y-2 text-gray-700">
                      {fullData.user_journey.map((usage: string, index: number) => (
                        <li key={index} className="leading-relaxed">{usage}</li>
                      ))}
                    </ol>
                  </div>
                )}
              </div>
            </div>

            {/* Admin Side */}
            <div className="space-y-6">
              <div className="bg-orange-50 p-4 rounded-lg border-l-4 border-l-orange-500">
                <h2 className="text-xl font-bold text-orange-800 mb-4 text-center">⚙️ Admin Management</h2>

                {/* Admin Features */}
                {fullData.admin_features && Array.isArray(fullData.admin_features) && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Admin Features</h3>
                    <ul className="list-disc list-inside space-y-2 text-gray-700">
                      {fullData.admin_features.map((item: string, index: number) => (
                        <li key={index} className="leading-relaxed">{item}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Admin Flow */}
                {fullData.admin_workflow && Array.isArray(fullData.admin_workflow) && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Admin Flow</h3>
                    <ol className="list-decimal list-inside space-y-2 text-gray-700">
                      {fullData.admin_workflow.map((usage: string, index: number) => (
                        <li key={index} className="leading-relaxed">{usage}</li>
                      ))}
                    </ol>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Notes */}
          {fullData.notes && Array.isArray(fullData.notes) && (
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-3 text-center">Notes</h2>
              <ul className="list-disc list-inside space-y-2 text-gray-700">
                {fullData.notes.map((note: string, index: number) => (
                  <li key={index} className="leading-relaxed">{note}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Color Scheme Display */}
      {project?.color_code && (
        <div className="mt-8">
          <ColorSchemeDisplay
            colorScheme={project.color_code}
            projectName={projectName}
          />
        </div>
      )}

    </div>
  );
}
