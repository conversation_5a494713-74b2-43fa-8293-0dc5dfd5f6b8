import { GoogleGenerativeAI, SchemaType } from '@google/generative-ai';
import { promptService } from './promptService';
import { excludedIdeasService } from './excludedIdeasService';
import { supabase } from '@/integrations/supabase/client';

// SaaS Idea Generation Response Schema
export interface SaasIdea {
  title: string;
  summary: string;
  description: string;
  category: string;
  target_user: string;
}

export interface IdeaGenerationResponse {
  ideas: SaasIdea[];
}

class IdeaGenerationService {
  private genAI: GoogleGenerativeAI;
  private readonly maxRetries = 5;
  private readonly retryDelay = 3000; // 3 seconds

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('VITE_GEMINI_API_KEY environment variable is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getResponseSchema() {
    return {
      type: SchemaType.OBJECT,
      required: ["ideas"],
      properties: {
        ideas: {
          type: SchemaType.ARRAY,
          items: {
            type: SchemaType.OBJECT,
            required: ["title", "summary", "description", "category", "target_user"],
            properties: {
              title: {
                type: SchemaType.STRING,
                description: "The name of the SaaS platform"
              },
              summary: {
                type: SchemaType.STRING,
                description: "Brief 5-10 word summary for display"
              },
              description: {
                type: SchemaType.STRING,
                description: "Detailed description of what the platform does and its value proposition"
              },
              category: {
                type: SchemaType.STRING,
                description: "Business category (e.g., Finance, HR, Marketplace, etc.)"
              },
              target_user: {
                type: SchemaType.STRING,
                description: "Who would use this platform (e.g., Small Businesses, Agencies, Freelancers, etc.)"
              }
            }
          }
        }
      }
    };
  }

  private async getSystemInstruction(): Promise<string> {
    return await promptService.getIdeaGenerationSystemPrompt();
  }

  private async getUserContent(existingProjects: string[], criticalNotes?: string): Promise<string> {
    // Add critical notes at the beginning with high priority if provided
    const criticalNotesSection = criticalNotes && criticalNotes.trim()
      ? `\n\n🚨 CRITICAL REQUIREMENTS (HIGHEST PRIORITY - MUST BE FOLLOWED):\n${criticalNotes.trim()}\n\nPlease ensure ALL generated ideas strictly follow the critical requirements above.`
      : '';

    const existingProjectsList = existingProjects.length > 0
      ? `\n\nExisting projects to avoid duplicating:\n${existingProjects.map(name => `- ${name}`).join('\n')}`
      : '';

    // Get existing categories and target users
    const { data: projectData, error } = await supabase
      .from('projects')
      .select('category, target_user');

    let categoriesAndTargetsInfo = '';
    if (!error && projectData) {
      const uniqueCategories = [...new Set(projectData.map(p => p.category).filter(Boolean))];
      const uniqueTargetUsers = [...new Set(projectData.map(p => p.target_user).filter(Boolean))];

      if (uniqueCategories.length > 0 || uniqueTargetUsers.length > 0) {
        categoriesAndTargetsInfo = '\n\nExisting categories and target users in our database:';
        if (uniqueCategories.length > 0) {
          categoriesAndTargetsInfo += `\nCategories: ${uniqueCategories.join(', ')}`;
        }
        if (uniqueTargetUsers.length > 0) {
          categoriesAndTargetsInfo += `\nTarget Users: ${uniqueTargetUsers.join(', ')}`;
        }
        categoriesAndTargetsInfo += '\nPlease try to use similar categories and target users when appropriate, but also feel free to suggest new ones.';
      }
    }

    // Get excluded ideas from database
    const excludedIdeas = await excludedIdeasService.getExcludedIdeasWithDetails();
    const excludedIdeasList = excludedIdeas.length > 0
      ? `\n\nExcluded ideas to never suggest again:\n${excludedIdeas.map(idea => {
          const description = idea.description ? ` - ${idea.description}` : '';
          return `- ${idea.title}${description}`;
        }).join('\n')}`
      : '';

    const basePrompt = await promptService.getIdeaGenerationUserPrompt();
    return `${basePrompt}${criticalNotesSection}${existingProjectsList}${categoriesAndTargetsInfo}${excludedIdeasList}`;
  }

  async generateIdeas(existingProjects: string[] = [], criticalNotes?: string): Promise<IdeaGenerationResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`Attempting to generate SaaS ideas (attempt ${attempt}/${this.maxRetries})`);

        const systemInstruction = await this.getSystemInstruction();
        const userPrompt = await this.getUserContent(existingProjects, criticalNotes);

        const model = this.genAI.getGenerativeModel({
          model: "gemini-2.5-pro",
          generationConfig: {
            temperature: 0.8,
            topP: 0.9,
            topK: 40,
            maxOutputTokens: 65536,
            responseMimeType: "application/json",
            responseSchema: this.getResponseSchema(),
          },
          systemInstruction,
        });
        const result = await model.generateContent(userPrompt);
        const response = result.response;
        const text = response.text();

        if (!text.trim()) {
          throw new Error('Empty response from Gemini API');
        }

        const parsedResponse = JSON.parse(text);

        // Filter out any excluded ideas that might have slipped through
        const filteredIdeas = await excludedIdeasService.filterOutExcludedIdeas(parsedResponse.ideas);
        const filteredResponse: IdeaGenerationResponse = {
          ideas: filteredIdeas
        };

        console.log('Successfully generated SaaS ideas:', filteredResponse);
        return filteredResponse;

      } catch (error) {
        lastError = error as Error;
        console.error(`Attempt ${attempt} failed:`, error);

        if (attempt < this.maxRetries) {
          console.log(`Retrying in ${this.retryDelay / 1000} seconds...`);
          await this.delay(this.retryDelay);
        }
      }
    }

    throw new Error(`Failed to generate ideas after ${this.maxRetries} attempts. Last error: ${lastError?.message}`);
  }
}

export const ideaGenerationService = new IdeaGenerationService();

// Project Variations Service
export interface ProjectVariation {
  title: string;
  description: string;
}

export interface ProjectVariationsResponse {
  variations: ProjectVariation[];
}

class ProjectVariationsService {
  private genAI: GoogleGenerativeAI;
  private readonly maxRetries = 3;
  private readonly retryDelay = 2000; // 2 seconds

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('VITE_GEMINI_API_KEY environment variable is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getResponseSchema() {
    return {
      type: SchemaType.OBJECT,
      required: ["variations"],
      properties: {
        variations: {
          type: SchemaType.ARRAY,
          items: {
            type: SchemaType.OBJECT,
            required: ["title", "description"],
            properties: {
              title: {
                type: SchemaType.STRING,
                description: "Alternative title for the project"
              },
              description: {
                type: SchemaType.STRING,
                description: "Alternative description for the project"
              }
            }
          }
        }
      }
    };
  }

  private getSystemInstruction(customInstruction?: string): string {
    const baseInstruction = `You are an expert SaaS naming and description specialist. Your task is to generate alternative titles and descriptions for existing SaaS project ideas.

Guidelines:
- Generate exactly 10 variations of the given project
- Each variation should have a different title and description but maintain the same core concept
- IMPORTANT: Format titles as "ProjectName - Short Summary" (e.g., "LegalLease - Business Legal Document Platform")
- The title should include both a catchy project name AND a brief descriptive summary separated by " - "
- Descriptions should be clear, concise, and highlight the value proposition (1-2 sentences)
- Avoid generic words like "generator", "builder", "template", "kit" in project names
- Focus on the benefit/outcome rather than the tool itself
- Maintain the same target audience and category as the original

The variations should feel fresh and different while staying true to the original concept.`;

    if (customInstruction && customInstruction.trim()) {
      return `${baseInstruction}

CRITICAL CUSTOM INSTRUCTION (HIGHEST PRIORITY):
${customInstruction.trim()}

Please follow the custom instruction above as the most important requirement when generating variations.`;
    }

    return baseInstruction;
  }

  private getUserContent(originalTitle: string, originalDescription: string): string {
    return `Generate 10 variations for this SaaS project:

Original Title: ${originalTitle}
Original Description: ${originalDescription}

Please provide 10 creative alternatives that maintain the same core concept but with different titles and descriptions. Each variation should feel unique while serving the same purpose and target audience.`;
  }

  async generateVariations(originalTitle: string, originalDescription: string, customInstruction?: string): Promise<ProjectVariationsResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`Attempting to generate project variations (attempt ${attempt}/${this.maxRetries})`);

        const model = this.genAI.getGenerativeModel({
          model: "gemini-2.5-pro",
          generationConfig: {
            temperature: 0.8,
            topP: 0.9,
            topK: 40,
            maxOutputTokens: 65536,
            responseMimeType: "application/json",
            responseSchema: this.getResponseSchema(),
          },
          systemInstruction: this.getSystemInstruction(customInstruction),
        });

        const userPrompt = this.getUserContent(originalTitle, originalDescription);
        const result = await model.generateContent(userPrompt);
        const response = result.response;
        const text = response.text();

        if (!text.trim()) {
          throw new Error('Empty response from Gemini API');
        }

        const parsedResponse = JSON.parse(text);
        console.log('Successfully generated project variations:', parsedResponse);
        return parsedResponse as ProjectVariationsResponse;

      } catch (error) {
        lastError = error as Error;
        console.error(`Attempt ${attempt} failed:`, error);

        if (attempt < this.maxRetries) {
          console.log(`Retrying in ${this.retryDelay / 1000} seconds...`);
          await this.delay(this.retryDelay);
        }
      }
    }

    throw new Error(`Failed to generate variations after ${this.maxRetries} attempts. Last error: ${lastError?.message}`);
  }
}

export const projectVariationsService = new ProjectVariationsService();




