-- Add landing_page_image column to projects table
ALTER TABLE public.projects
ADD COLUMN landing_page_image TEXT;

-- Add comment to describe the column
COMMENT ON COLUMN public.projects.landing_page_image IS 'URL to the generated landing page preview image stored in Supabase Storage';

-- Add index for better performance when filtering projects with images
CREATE INDEX idx_projects_landing_page_image ON public.projects(landing_page_image) WHERE landing_page_image IS NOT NULL;

-- Create storage bucket for landing page images
INSERT INTO storage.buckets (id, name, public)
VALUES ('landing-page-images', 'landing-page-images', true)
ON CONFLICT (id) DO NOTHING;

-- Set up RLS policy for the storage bucket
CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (bucket_id = 'landing-page-images');
CREATE POLICY "Authenticated users can upload landing page images" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'landing-page-images' AND auth.role() = 'authenticated');
