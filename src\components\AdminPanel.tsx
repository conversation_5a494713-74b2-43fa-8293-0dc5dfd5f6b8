import React, { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";
import { JsonContentEditor } from "@/components/JsonContentEditor";
import { MermaidEditor } from "@/components/MermaidEditor";
import { Project, ProjectContent, normalizeProjectContent, defaultProjectContent } from "@/types/project";
import { geminiService } from "@/services/geminiService";
import { cloudflareFluxService } from "@/services/cloudflareFluxService";
import { FlexibleJsonRenderer } from "@/components/FlexibleJsonRenderer";
import { Loader2, Sparkles, CheckCircle, Clock, ExternalLink, Lightbulb, Settings, Image, RefreshCw, Palette } from "lucide-react";
import { IdeaGenerationModal } from "@/components/IdeaGenerationModal";
import { PromptEditor } from "@/components/PromptEditor";
import { ProjectVariationsModal } from "@/components/ProjectVariationsModal";
import { CriticalNotesModal } from "@/components/CriticalNotesModal";
import { IdeaCriticalNotesModal } from "@/components/IdeaCriticalNotesModal";
import { useIdeaGeneration } from "@/hooks/useIdeaGeneration";
import { projectVariationsService, ProjectVariation } from "@/services/ideaGenerationService";

export function AdminPanel() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [generatingProjects, setGeneratingProjects] = useState<Set<string>>(new Set());
  const [generatingColorCodes, setGeneratingColorCodes] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState("pending");
  const [isPromptEditorOpen, setIsPromptEditorOpen] = useState(false);
  const [isTestImageModalOpen, setIsTestImageModalOpen] = useState(false);
  const [testImageUrl, setTestImageUrl] = useState<string | null>(null);
  const [isGeneratingTestImage, setIsGeneratingTestImage] = useState(false);
  const [selectedTestProject, setSelectedTestProject] = useState<Project | null>(null);
  const [isProjectSelectionModalOpen, setIsProjectSelectionModalOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState('@cf/black-forest-labs/flux-1-schnell');
  const [promptPreview, setPromptPreview] = useState<string>('');
  const [isGeneratingPromptPreview, setIsGeneratingPromptPreview] = useState(false);

  // Project variations state
  const [isVariationsModalOpen, setIsVariationsModalOpen] = useState(false);
  const [isGeneratingVariations, setIsGeneratingVariations] = useState(false);
  const [projectVariations, setProjectVariations] = useState<ProjectVariation[]>([]);
  const [currentVariationProject, setCurrentVariationProject] = useState<Project | null>(null);

  // Critical notes modal state
  const [isCriticalNotesModalOpen, setIsCriticalNotesModalOpen] = useState(false);
  const [currentGeneratingProject, setCurrentGeneratingProject] = useState<Project | null>(null);

  // Idea critical notes modal state
  const [isIdeaCriticalNotesModalOpen, setIsIdeaCriticalNotesModalOpen] = useState(false);

  // Mermaid editor state
  const [isMermaidEditorOpen, setIsMermaidEditorOpen] = useState(false);
  const [currentMermaidProject, setCurrentMermaidProject] = useState<Project | null>(null);

  // Idea generation hook
  const {
    isModalOpen: isIdeaModalOpen,
    isGenerating: isGeneratingIdeas,
    generatedIdeas,
    generateIdeas,
    saveSelectedIdeas,
    closeModal: closeIdeaModal,
  } = useIdeaGeneration();

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "",
    target_user: "",
    status: "Unclaimed",
    claimed_by: "",
    content: defaultProjectContent,
    pricing_tier: 100,
  });

  useEffect(() => {
    fetchProjects();
  }, []);

  // Helper function to check if project has AI-generated content
  const hasAIGeneratedContent = (project: Project): boolean => {
    if (!project.content || typeof project.content !== 'object') {
      return false;
    }

    // Check for AI-generated content structure (MVPResponse fields)
    const content = project.content as any;
    return !!(
      content.project_description ||
      content.main_user_features ||
      content.homepage ||
      content.user_journey ||
      content.admin_features ||
      content.flow_diagram
    );
  };

  // Helper function to check if project has a flow diagram
  const hasFlowDiagram = (project: Project): boolean => {
    if (!project.content || typeof project.content !== 'object') {
      return false;
    }

    const content = project.content as any;
    return !!(content.flow_diagram && typeof content.flow_diagram === 'string' && content.flow_diagram.trim().length > 0);
  };

  // Helper functions to categorize projects
  const getPendingProjects = (): Project[] => {
    return projects.filter(project => !hasAIGeneratedContent(project));
  };

  const getProcessedProjects = (): Project[] => {
    return projects.filter(project => hasAIGeneratedContent(project));
  };

  const getPendingCount = (): number => getPendingProjects().length;
  const getProcessedCount = (): number => getProcessedProjects().length;

  // Helper functions for projects needing landing page images
  const getProjectsNeedingLandingPage = (): Project[] => {
    return projects.filter(project =>
      hasAIGeneratedContent(project) && !project.landing_page_image
    );
  };

  const getProjectsNeedingLandingPageCount = (): number => getProjectsNeedingLandingPage().length;

  // Helper functions for projects needing color codes
  const getProjectsNeedingColorCode = (): Project[] => {
    return projects.filter(project =>
      hasAIGeneratedContent(project) && !project.color_code
    );
  };

  const getProjectsNeedingColorCodeCount = (): number => getProjectsNeedingColorCode().length;

  // Handle saving selected ideas and refresh project list
  const handleSaveIdeas = async (selectedIdeas: any[]) => {
    const success = await saveSelectedIdeas(selectedIdeas);
    if (success) {
      fetchProjects(); // Refresh the project list
    }
  };

  // Handle opening the idea critical notes modal
  const handleGenerateIdeasClick = () => {
    setIsIdeaCriticalNotesModalOpen(true);
  };

  // Handle generating ideas with critical notes
  const handleGenerateIdeasWithNotes = async (criticalNotes?: string) => {
    setIsIdeaCriticalNotesModalOpen(false);
    await generateIdeas(criticalNotes);
  };

  // Handle closing the idea critical notes modal
  const handleCloseIdeaCriticalNotesModal = () => {
    setIsIdeaCriticalNotesModalOpen(false);
  };

  // Helper function to parse project name and get title and summary
  const parseProjectName = (name: string) => {
    // Check if name contains " - " separator
    const separatorIndex = name.indexOf(' - ');
    if (separatorIndex !== -1) {
      return {
        title: name.substring(0, separatorIndex),
        summary: name.substring(separatorIndex + 3)
      };
    }
    // If no separator, treat the whole name as title
    return {
      title: name,
      summary: ''
    };
  };

  // Component to render project cards for a specific category
  const ProjectCards = ({
    categoryProjects,
    isPending,
    showLandingPageButton = false,
    showRegenerateButton = false,
    showColorCodeButton = false,
    onGenerateLandingPage,
    onRegenerateMVP,
    onGenerateColorCode,
    generatingColorCodes
  }: {
    categoryProjects: Project[],
    isPending: boolean,
    showLandingPageButton?: boolean,
    showRegenerateButton?: boolean,
    showColorCodeButton?: boolean,
    onGenerateLandingPage?: (project: Project) => void,
    onRegenerateMVP?: (project: Project) => void,
    onGenerateColorCode?: (project: Project) => void,
    generatingColorCodes?: Set<string>
  }) => {
    if (categoryProjects.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">
            {isPending ? "No projects pending AI generation." : "No projects yet."}
          </p>
        </div>
      );
    }

    return (
      <div className="grid gap-6">
        {categoryProjects.map((project) => {
          const { title, summary } = parseProjectName(project.name);
          const displayName = summary ? `${title} - ${summary}` : title;

          return (
            <Card key={project.id}>
              <CardHeader>
                <CardTitle className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <span>{displayName}</span>
                  {isPending ? (
                    <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                      <Clock className="h-3 w-3 mr-1" />
                      Pending
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Generated
                    </Badge>
                  )}
                  {!isPending && project.pricing_tier && (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      ${project.pricing_tier}
                    </Badge>
                  )}
                </div>
                <div className="space-x-2">
                  {isPending && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleGenerateMVP(project)}
                        disabled={generatingProjects.has(project.id)}
                      >
                        {generatingProjects.has(project.id) ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Sparkles className="h-4 w-4 mr-1" />
                            Generate
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleGetVariations(project)}
                        disabled={isGeneratingVariations}
                      >
                        <RefreshCw className="h-4 w-4 mr-1" />
                        Get Variations
                      </Button>
                    </>
                  )}
                  {!isPending && (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => window.open(`/project/${project.id}`, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      View Project
                    </Button>
                  )}
                  {showRegenerateButton && onRegenerateMVP && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onRegenerateMVP(project)}
                      disabled={generatingProjects.has(project.id)}
                    >
                      {generatingProjects.has(project.id) ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          Re-generating...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-1" />
                          Re-generate
                        </>
                      )}
                    </Button>
                  )}
                  {showLandingPageButton && onGenerateLandingPage && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onGenerateLandingPage(project)}
                      disabled={generatingProjects.has(project.id)}
                    >
                      {generatingProjects.has(project.id) ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Image className="h-4 w-4 mr-1" />
                          Generate Landing Page
                        </>
                      )}
                    </Button>
                  )}
                  {!isPending && !showLandingPageButton && project.landing_page_image && onGenerateLandingPage && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onGenerateLandingPage(project)}
                      disabled={generatingProjects.has(project.id)}
                    >
                      {generatingProjects.has(project.id) ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          Regenerating...
                        </>
                      ) : (
                        <>
                          <Image className="h-4 w-4 mr-1" />
                          Regenerate Landing Page
                        </>
                      )}
                    </Button>
                  )}
                  {showColorCodeButton && onGenerateColorCode && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onGenerateColorCode(project)}
                      disabled={generatingColorCodes?.has(project.id)}
                    >
                      {generatingColorCodes?.has(project.id) ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Palette className="h-4 w-4 mr-1" />
                          Generate Color Code
                        </>
                      )}
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(project)}
                  >
                    Edit
                  </Button>
                  {hasFlowDiagram(project) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditMermaidDiagram(project)}
                      className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                    >
                      Edit Diagram
                    </Button>
                  )}
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(project.id)}
                  >
                    Delete
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Description:</strong> {project.description}</p>
                  <p><strong>Category:</strong> {project.category}</p>
                  <p><strong>Target User:</strong> {project.target_user}</p>
                  <p><strong>Status:</strong> {project.status}</p>
                  {project.claimed_by && <p><strong>Claimed By:</strong> {project.claimed_by}</p>}
                </div>

                {/* Landing Page Image Preview */}
                {project.landing_page_image && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900">Landing Page Preview</h4>
                    <div className="border rounded-lg overflow-hidden bg-gray-50">
                      <img
                        src={project.landing_page_image}
                        alt={`Landing page preview for ${project.name}`}
                        className="w-full h-48 object-cover hover:scale-105 transition-transform duration-200 cursor-pointer"
                        onClick={() => window.open(project.landing_page_image!, '_blank')}
                        onError={(e) => {
                          console.error('Failed to load image:', project.landing_page_image);
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    </div>
                    <p className="text-xs text-gray-500">Click image to view full size</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          );
        })}
      </div>
    );
  };

  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from("projects")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      // Add default pricing_tier for projects that don't have it and fix types
      const projectsWithPricing = (data || []).map(project => ({
        ...project,
        pricing_tier: (project as any).pricing_tier || 100,
        content: project.content as any
      })) as Project[];
      setProjects(projectsWithPricing);
    } catch (error) {
      console.error("Error fetching projects:", error);
      toast({
        title: "Error",
        description: "Failed to fetch projects",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      category: "",
      target_user: "",
      status: "Unclaimed",
      claimed_by: "",
      content: defaultProjectContent,
      pricing_tier: 100,
    });
  };

  const handleAdd = async () => {
    try {
      const { error } = await supabase.from("projects").insert([{
        ...formData,
        claimed_by: formData.claimed_by || null,
        content: formData.content as any,
      }]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Project added successfully",
      });

      fetchProjects();
      setIsAddModalOpen(false);
      resetForm();
    } catch (error) {
      console.error("Error adding project:", error);
      toast({
        title: "Error",
        description: "Failed to add project",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (project: Project) => {
    setEditingProject(project);
    setFormData({
      name: project.name,
      description: project.description,
      category: project.category,
      target_user: project.target_user,
      status: project.status,
      claimed_by: project.claimed_by || "",
      content: project.content || defaultProjectContent, // Keep the original content structure
      pricing_tier: project.pricing_tier || 100,
    });
    setIsEditModalOpen(true);
  };

  const handleUpdate = async () => {
    if (!editingProject) return;

    try {
      const { error } = await supabase
        .from("projects")
        .update({
          ...formData,
          claimed_by: formData.claimed_by || null,
          content: formData.content as any,
        })
        .eq("id", editingProject.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Project updated successfully",
      });

      fetchProjects();
      setIsEditModalOpen(false);
      setEditingProject(null);
      resetForm();
    } catch (error) {
      console.error("Error updating project:", error);
      toast({
        title: "Error",
        description: "Failed to update project",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this project?")) return;

    try {
      const { error } = await supabase.from("projects").delete().eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Project deleted successfully",
      });

      fetchProjects();
    } catch (error) {
      console.error("Error deleting project:", error);
      toast({
        title: "Error",
        description: "Failed to delete project",
        variant: "destructive",
      });
    }
  };

  const handleGenerateMVP = (project: Project) => {
    setCurrentGeneratingProject(project);
    setIsCriticalNotesModalOpen(true);
  };

  const handleGenerateMVPWithNotes = async (criticalNotes: string) => {
    if (!currentGeneratingProject) return;

    const project = currentGeneratingProject;
    const projectId = project.id;

    // Close modal and clear current project
    setIsCriticalNotesModalOpen(false);
    setCurrentGeneratingProject(null);

    // Add project to generating set
    setGeneratingProjects(prev => new Set(prev).add(projectId));

    try {
      toast({
        title: "Generating MVP",
        description: `Starting MVP generation for "${project.name}"...`,
      });

      // Generate MVP data with critical notes
      const mvpData = await geminiService.generateMVP(project.name, criticalNotes || undefined);

      // Update the project with the generated MVP data and pricing tier
      const { error } = await supabase
        .from("projects")
        .update({
          content: mvpData as any,
          pricing_tier: mvpData.complexity_tier || 100, // Default to $100 if not specified
        })
        .eq("id", projectId);

      if (error) throw error;

      toast({
        title: "Success",
        description: `MVP generated successfully for "${project.name}"`,
      });

      fetchProjects();
    } catch (error) {
      console.error("Error generating MVP:", error);
      toast({
        title: "Error",
        description: `Failed to generate MVP: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      // Remove project from generating set
      setGeneratingProjects(prev => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    }
  };

  const handleCloseCriticalNotesModal = () => {
    setIsCriticalNotesModalOpen(false);
    setCurrentGeneratingProject(null);
  };

  // Handle Mermaid diagram editing
  const handleEditMermaidDiagram = (project: Project) => {
    setCurrentMermaidProject(project);
    setIsMermaidEditorOpen(true);
  };

  const handleSaveMermaidDiagram = async (diagramCode: string) => {
    if (!currentMermaidProject) return;

    try {
      // Get the current project content
      const currentContent = currentMermaidProject.content || {};

      // Update only the flow_diagram field
      const updatedContent = {
        ...currentContent,
        flow_diagram: diagramCode
      };

      const { error } = await supabase
        .from("projects")
        .update({
          content: updatedContent as any,
        })
        .eq("id", currentMermaidProject.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Flow diagram updated successfully",
      });

      fetchProjects();
      setIsMermaidEditorOpen(false);
      setCurrentMermaidProject(null);
    } catch (error) {
      console.error("Error updating flow diagram:", error);
      toast({
        title: "Error",
        description: "Failed to update flow diagram",
        variant: "destructive",
      });
    }
  };

  const handleCancelMermaidEdit = () => {
    setIsMermaidEditorOpen(false);
    setCurrentMermaidProject(null);
  };

  const handleEnhanceMVP = async (enhancementRequests: string) => {
    if (!currentGeneratingProject) return;

    const project = currentGeneratingProject;
    const projectId = project.id;

    // Close modal and clear current project
    setIsCriticalNotesModalOpen(false);
    setCurrentGeneratingProject(null);

    // Add project to generating set
    setGeneratingProjects(prev => new Set(prev).add(projectId));

    try {
      toast({
        title: "Enhancing MVP",
        description: `Starting MVP enhancement for "${project.name}"...`,
      });

      // Enhance MVP data with existing content
      const enhancedData = await geminiService.enhanceMVP(
        project.name,
        project.content,
        enhancementRequests || undefined
      );

      // Update the project with the enhanced MVP data and pricing tier
      const { error } = await supabase
        .from("projects")
        .update({
          content: enhancedData as any,
          pricing_tier: enhancedData.complexity_tier || project.pricing_tier || 100,
        })
        .eq("id", projectId);

      if (error) throw error;

      toast({
        title: "Success",
        description: `MVP enhanced successfully for "${project.name}"`,
      });

      fetchProjects();
    } catch (error) {
      console.error("Error enhancing MVP:", error);
      toast({
        title: "Error",
        description: `Failed to enhance MVP: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      // Remove project from generating set
      setGeneratingProjects(prev => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    }
  };

  // Handle color code generation
  const handleGenerateColorCode = async (project: Project) => {
    const projectId = project.id;

    // Add project to generating set
    setGeneratingColorCodes(prev => new Set(prev).add(projectId));

    try {
      toast({
        title: "Generating Color Code",
        description: `Starting color scheme generation for "${project.name}"...`,
      });

      // Fetch existing color codes to ensure uniqueness
      const { data: existingProjects, error: fetchError } = await supabase
        .from("projects")
        .select("color_code")
        .not("color_code", "is", null);

      if (fetchError) {
        console.error("Error fetching existing color codes:", fetchError);
      }

      // Extract primary colors from existing projects
      const existingPrimaryColors = (existingProjects || [])
        .map(p => p.color_code?.primary)
        .filter(Boolean) as string[];

      console.log("Existing primary colors:", existingPrimaryColors);

      // Generate color scheme with uniqueness check
      const colorScheme = await geminiService.generateColorScheme(
        project.name,
        project.description,
        project.category,
        project.target_user,
        existingPrimaryColors
      );

      // Update the project with the generated color scheme
      const { error } = await supabase
        .from("projects")
        .update({
          color_code: colorScheme as any,
        })
        .eq("id", projectId);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Unique color scheme generated successfully for "${project.name}"`,
      });

      fetchProjects();
    } catch (error) {
      console.error("Error generating color scheme:", error);
      toast({
        title: "Error",
        description: `Failed to generate color scheme: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      // Remove project from generating set
      setGeneratingColorCodes(prev => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    }
  };

  // Handle landing page image generation
  // Handle project variations generation
  const handleGetVariations = async (project: Project) => {
    setCurrentVariationProject(project);
    setIsVariationsModalOpen(true);
    setProjectVariations([]);
  };

  // Handle variations generation with custom instruction
  const handleGenerateVariations = async (customInstruction: string) => {
    if (!currentVariationProject) return;

    setIsGeneratingVariations(true);
    setProjectVariations([]);

    try {
      // Parse the project name to get just the title part for AI generation
      const { title } = parseProjectName(currentVariationProject.name);

      const variations = await projectVariationsService.generateVariations(
        title, // Use just the title part, not the full "title - summary" format
        currentVariationProject.description,
        customInstruction
      );
      setProjectVariations(variations.variations);
    } catch (error) {
      console.error("Error generating variations:", error);
      toast({
        title: "Error",
        description: `Failed to generate variations: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
      setIsVariationsModalOpen(false);
    } finally {
      setIsGeneratingVariations(false);
    }
  };

  // Handle variation selection and project update
  const handleSelectVariation = async (variation: ProjectVariation) => {
    if (!currentVariationProject) return;

    try {
      // The variation.title from AI should be formatted as "Title - Summary"
      // and variation.description should be the detailed description
      const { error } = await supabase
        .from("projects")
        .update({
          name: variation.title, // This should already be in "Title - Summary" format from AI
          description: variation.description,
        })
        .eq("id", currentVariationProject.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Project updated with selected variation",
      });

      fetchProjects();
      setIsVariationsModalOpen(false);
      setCurrentVariationProject(null);
      setProjectVariations([]);
    } catch (error) {
      console.error("Error updating project:", error);
      toast({
        title: "Error",
        description: "Failed to update project with variation",
        variant: "destructive",
      });
    }
  };

  const handleGenerateLandingPage = async (project: Project) => {
    const projectId = project.id;

    // Add project to generating set
    setGeneratingProjects(prev => new Set(prev).add(projectId));

    try {
      toast({
        title: "Generating Landing Page Image",
        description: `Creating landing page image for "${project.name}"...`,
      });

      // Generate landing page image using AI-generated prompt based on project content
      const imageUrl = await cloudflareFluxService.generateLandingPageImageWithAI(
        project.name,
        project.description,
        project.content
      );

      // Update the project with the landing page image
      const { error } = await supabase
        .from("projects")
        .update({
          landing_page_image: imageUrl
        })
        .eq("id", projectId);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Landing page image generated successfully for "${project.name}"`,
      });

      fetchProjects();
    } catch (error) {
      console.error("Error generating landing page image:", error);
      toast({
        title: "Error",
        description: `Failed to generate landing page image: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      // Remove project from generating set
      setGeneratingProjects(prev => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    }
  };

  const handleTestImageGeneration = async (project: Project) => {
    setIsGeneratingTestImage(true);
    setTestImageUrl(null);
    setSelectedTestProject(project);

    try {
      toast({
        title: "Generating Test Image",
        description: `Creating landing page image for "${project.name}"...`,
      });

      // Use the real image generation implementation with selected model
      const imageUrl = await cloudflareFluxService.generateLandingPageImage(
        project.name,
        project.description,
        selectedModel
      );

      setTestImageUrl(imageUrl);
      setIsTestImageModalOpen(true);

      toast({
        title: "Success",
        description: `Test image generated successfully for "${project.name}"!`,
      });

    } catch (error) {
      console.error("Error generating test image:", error);
      toast({
        title: "Error",
        description: `Failed to generate test image: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsGeneratingTestImage(false);
    }
  };

  const openTestImageModal = () => {
    setIsTestImageModalOpen(true);
  };

  // Generate prompt preview for selected project
  const generatePromptPreview = async (project: Project) => {
    if (!project) return;

    setIsGeneratingPromptPreview(true);
    try {
      let prompt = '';

      // Check if project has AI-generated content to use the AI prompt generation
      if (hasAIGeneratedContent(project)) {
        prompt = await geminiService.generateLandingPagePrompt(
          project.name,
          project.description,
          project.content
        );
      } else {
        // Use the legacy hardcoded prompt for projects without AI content
        prompt = `Complete full-length landing page mockup for SaaS application "${project.name}" in ultra HD quality.

Project: ${project.description}

Show ENTIRE page from header to footer including:
- Header with navigation bar and logo
- Hero section with compelling headline and CTA button
- Features section with benefit cards and icons
- Pricing or testimonials section
- Footer with links and contact info

Style: Modern, clean, professional SaaS design, high resolution, detailed UI elements, contemporary color scheme, desktop view, complete scrollable page layout from top to bottom, ultra HD quality`;
      }

      // Ensure prompt doesn't exceed Cloudflare's 2048 character limit
      if (prompt.length > 2048) {
        prompt = prompt.substring(0, 2045) + '...';
      }

      setPromptPreview(prompt);
    } catch (error) {
      console.error('Error generating prompt preview:', error);
      setPromptPreview('Error generating prompt preview. Please try again.');
    } finally {
      setIsGeneratingPromptPreview(false);
    }
  };

  const ProjectForm = ({ onSubmit, submitText }: { onSubmit: () => void; submitText: string }) => (
    <div className="space-y-4">
      <Input
        placeholder="Project Name"
        value={formData.name}
        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
      />
      <Textarea
        placeholder="Description"
        value={formData.description}
        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
      />
      <Input
        placeholder="Category"
        value={formData.category}
        onChange={(e) => setFormData({ ...formData, category: e.target.value })}
      />
      <Input
        placeholder="Target User"
        value={formData.target_user}
        onChange={(e) => setFormData({ ...formData, target_user: e.target.value })}
      />
      <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="Unclaimed">Unclaimed</SelectItem>
          <SelectItem value="Claimed">Claimed</SelectItem>
          <SelectItem value="In Progress">In Progress</SelectItem>
          <SelectItem value="Completed">Completed</SelectItem>
        </SelectContent>
      </Select>
      <Input
        placeholder="Claimed By (optional)"
        value={formData.claimed_by}
        onChange={(e) => setFormData({ ...formData, claimed_by: e.target.value })}
      />
      <div>
        <label className="text-sm font-medium mb-2 block">Pricing Tier</label>
        <Select value={formData.pricing_tier.toString()} onValueChange={(value) => setFormData({ ...formData, pricing_tier: parseInt(value) })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="50">$50 - Simple</SelectItem>
            <SelectItem value="100">$100 - Standard</SelectItem>
            <SelectItem value="200">$200 - Complex</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <label className="text-sm font-medium mb-2 block">Project Content</label>
        <JsonContentEditor
          content={formData.content}
          onChange={(content) => setFormData({ ...formData, content })}
        />
      </div>
      <Button onClick={onSubmit} className="w-full">
        {submitText}
      </Button>
    </div>
  );

  if (loading) {
    return <div className="p-8 text-center">Loading admin panel...</div>;
  }

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Admin Panel</h1>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleGenerateIdeasClick}
              disabled={isGeneratingIdeas}
            >
              {isGeneratingIdeas ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Lightbulb className="h-4 w-4 mr-2" />
                  Generate Ideas
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsPromptEditorOpen(true)}
            >
              <Settings className="h-4 w-4 mr-2" />
              Edit Prompts
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsProjectSelectionModalOpen(true)}
              disabled={isGeneratingTestImage}
            >
              {isGeneratingTestImage ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Test Image Generation
                </>
              )}
            </Button>
            <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
              <DialogTrigger asChild>
                <Button>Add New Project</Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Project</DialogTitle>
                </DialogHeader>
                <div className="max-h-[calc(90vh-120px)] overflow-y-auto pr-2">
                  <ProjectForm onSubmit={handleAdd} submitText="Add Project" />
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="mb-6">
          <p className="text-sm text-gray-600">
            Total: {projects.length} project{projects.length !== 1 ? 's' : ''}
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pending" className="flex items-center gap-2">
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                <Clock className="h-3 w-3 mr-1" />
                Pending
              </Badge>
              ({getPendingCount()})
            </TabsTrigger>
            <TabsTrigger value="processed" className="flex items-center gap-2">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="h-3 w-3 mr-1" />
                AI Generated
              </Badge>
              ({getProcessedCount()})
            </TabsTrigger>
            <TabsTrigger value="needsLandingPage" className="flex items-center gap-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                <Image className="h-3 w-3 mr-1" />
                Needs Landing Page
              </Badge>
              ({getProjectsNeedingLandingPageCount()})
            </TabsTrigger>
            <TabsTrigger value="needsColorCode" className="flex items-center gap-2">
              <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                <Palette className="h-3 w-3 mr-1" />
                Color Code
              </Badge>
              ({getProjectsNeedingColorCodeCount()})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="mt-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Projects Pending AI Generation</h3>
              <p className="text-sm text-gray-600">These projects need to be processed with AI to generate detailed specifications.</p>
            </div>
            <ProjectCards categoryProjects={getPendingProjects()} isPending={true} />
          </TabsContent>

          <TabsContent value="processed" className="mt-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Projects</h3>
              <p className="text-sm text-gray-600">These projects have been processed and contain detailed AI-generated specifications. You can re-generate the content if needed.</p>
            </div>
            <ProjectCards
              categoryProjects={getProcessedProjects()}
              isPending={false}
              showRegenerateButton={true}
              onGenerateLandingPage={handleGenerateLandingPage}
              onRegenerateMVP={handleGenerateMVP}
            />
          </TabsContent>

          <TabsContent value="needsLandingPage" className="mt-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Projects Needing Landing Page Images</h3>
              <p className="text-sm text-gray-600">These projects need landing page images to be created.</p>
            </div>
            <ProjectCards
              categoryProjects={getProjectsNeedingLandingPage()}
              isPending={false}
              showLandingPageButton={true}
              onGenerateLandingPage={handleGenerateLandingPage}
            />
          </TabsContent>

          <TabsContent value="needsColorCode" className="mt-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Projects Needing Color Codes</h3>
              <p className="text-sm text-gray-600">These projects need color schemes to be generated.</p>
            </div>
            <ProjectCards
              categoryProjects={getProjectsNeedingColorCode()}
              isPending={false}
              showColorCodeButton={true}
              onGenerateColorCode={handleGenerateColorCode}
              generatingColorCodes={generatingColorCodes}
            />
          </TabsContent>
        </Tabs>

        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Project</DialogTitle>
            </DialogHeader>
            <div className="max-h-[calc(90vh-120px)] overflow-y-auto pr-2">
              <ProjectForm onSubmit={handleUpdate} submitText="Update Project" />
            </div>
          </DialogContent>
        </Dialog>

        {/* Idea Generation Modal */}
        <IdeaGenerationModal
          isOpen={isIdeaModalOpen}
          onClose={closeIdeaModal}
          ideas={generatedIdeas}
          isLoading={isGeneratingIdeas}
          onSaveSelected={handleSaveIdeas}
        />

        {/* Idea Critical Notes Modal */}
        <IdeaCriticalNotesModal
          isOpen={isIdeaCriticalNotesModalOpen}
          onClose={handleCloseIdeaCriticalNotesModal}
          onGenerate={handleGenerateIdeasWithNotes}
          isGenerating={isGeneratingIdeas}
        />

        {/* Prompt Editor Modal */}
        <PromptEditor
          isOpen={isPromptEditorOpen}
          onClose={() => setIsPromptEditorOpen(false)}
        />

        {/* Project Selection Modal for Test Image Generation */}
        <Dialog
          open={isProjectSelectionModalOpen}
          onOpenChange={(open) => {
            setIsProjectSelectionModalOpen(open);
            if (!open) {
              setPromptPreview(''); // Clear prompt preview when modal closes
            }
          }}
        >
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Select Project for Image Generation Test</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Choose a project to generate a landing page image for testing purposes.
              </p>

              <div className="space-y-2">
                <label className="text-sm font-medium">AI Model:</label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="@cf/black-forest-labs/flux-1-schnell">
                      Flux 1 Schnell (High Quality, Slower)
                    </SelectItem>
                    <SelectItem value="@cf/bytedance/stable-diffusion-xl-lightning">
                      SDXL Lightning (Fast, Good Quality)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Prompt Preview Section */}
              {promptPreview && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Generated Prompt Preview:</label>
                  <div className="bg-gray-50 border rounded-lg p-4 max-h-40 overflow-y-auto">
                    <pre className="text-xs text-gray-700 whitespace-pre-wrap font-mono">
                      {promptPreview}
                    </pre>
                  </div>
                  <div className="text-xs text-gray-500">
                    Character count: {promptPreview.length}/2048
                    {promptPreview.length > 2048 && (
                      <span className="text-red-500 ml-2">⚠️ Prompt will be truncated</span>
                    )}
                  </div>
                </div>
              )}

              <div className="grid gap-4 max-h-[60vh] overflow-y-auto">
                {projects.map((project) => (
                  <Card key={project.id} className="cursor-pointer hover:bg-gray-50 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-medium text-lg mb-2">{project.name}</h3>
                          <p className="text-sm text-gray-600 mb-2">{project.description}</p>
                          <div className="flex gap-2 text-xs text-gray-500">
                            <span>Category: {project.category}</span>
                            <span>•</span>
                            <span>Target: {project.target_user}</span>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            onClick={() => generatePromptPreview(project)}
                            disabled={isGeneratingPromptPreview}
                            size="sm"
                          >
                            {isGeneratingPromptPreview ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                Loading...
                              </>
                            ) : (
                              'Preview Prompt'
                            )}
                          </Button>
                          <Button
                            onClick={() => {
                              setIsProjectSelectionModalOpen(false);
                              handleTestImageGeneration(project);
                            }}
                            size="sm"
                          >
                            <Sparkles className="h-4 w-4 mr-1" />
                            Generate Image
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {projects.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No projects available. Add some projects first.
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Test Image Generation Modal */}
        <Dialog open={isTestImageModalOpen} onOpenChange={setIsTestImageModalOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Test Image Generation Result</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {selectedTestProject && (
                <div className="text-sm text-gray-600 bg-gray-50 p-4 rounded-lg">
                  <p><strong>Project:</strong> {selectedTestProject.name}</p>
                  <p><strong>Description:</strong> {selectedTestProject.description}</p>
                  <p><strong>Category:</strong> {selectedTestProject.category}</p>
                  <p><strong>Target User:</strong> {selectedTestProject.target_user}</p>
                </div>
              )}

              {testImageUrl ? (
                <div className="space-y-4">
                  <div className="border rounded-lg overflow-hidden bg-gray-50">
                    <img
                      src={testImageUrl}
                      alt="Generated test landing page"
                      className="w-full h-auto object-contain"
                      onError={(e) => {
                        console.error('Failed to load test image:', testImageUrl);
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => window.open(testImageUrl, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open in New Tab
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        navigator.clipboard.writeText(testImageUrl);
                        toast({
                          title: "Copied!",
                          description: "Image URL copied to clipboard",
                        });
                      }}
                    >
                      Copy URL
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No image generated yet. Click "Test Image Generation" to create one.
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Project Variations Modal */}
        <ProjectVariationsModal
          isOpen={isVariationsModalOpen}
          onClose={() => {
            setIsVariationsModalOpen(false);
            setCurrentVariationProject(null);
            setProjectVariations([]);
          }}
          variations={projectVariations}
          isLoading={isGeneratingVariations}
          onSelectVariation={handleSelectVariation}
          onGenerateVariations={handleGenerateVariations}
          originalTitle={currentVariationProject?.name || ''}
          originalDescription={currentVariationProject?.description || ''}
        />

        {/* Critical Notes Modal */}
        <CriticalNotesModal
          isOpen={isCriticalNotesModalOpen}
          onClose={handleCloseCriticalNotesModal}
          onGenerate={handleGenerateMVPWithNotes}
          onEnhance={handleEnhanceMVP}
          projectName={currentGeneratingProject?.name || ''}
          isGenerating={currentGeneratingProject ? generatingProjects.has(currentGeneratingProject.id) : false}
          hasExistingContent={currentGeneratingProject ? hasAIGeneratedContent(currentGeneratingProject) : false}
        />

        {/* Mermaid Editor Modal */}
        <Dialog open={isMermaidEditorOpen} onOpenChange={setIsMermaidEditorOpen}>
          <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                Edit Flow Diagram - {currentMermaidProject?.name}
              </DialogTitle>
            </DialogHeader>
            <div className="max-h-[calc(95vh-120px)] overflow-y-auto pr-2">
              {currentMermaidProject && (
                <MermaidEditor
                  diagramCode={(currentMermaidProject.content as any)?.flow_diagram || ''}
                  onSave={handleSaveMermaidDiagram}
                  onCancel={handleCancelMermaidEdit}
                />
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}