# Must-Have Features for All SaaS

This is just an example, normally all SaaS will contain those features, but there will be indeed some additional features.

### For Users:
- Signup & Login (no email confirmation required)
- Manage Profile (view and edit profile, change password)
- Subscription and Billing
  - A. Plan options (one-time or recurring - but must only be monthly) - Hardcode it, no need to store in the database
  - B. Plan options (Try to use only 1 or 2 plan - depends on project)
  - Stripe integration for payments
  - View billing history
- User Dashboard (depends on the project, sometime it might include CMS also)

### For Admins:
- Simple Admin panel to manage users only (view, edit, assign packages, etc.)
- No other admin features are required

### Homepage (Only the homepage is needed):
- Header with Signup/Login button  
  → Show user’s name when logged in
- Hero section
- Main content section
- Footer with links (e.g., /documentation, /pricing, etc.)  
  *(No need to build these pages - just show links)*

### Technologies:
- React (Frontend)
- Supabase (Database, auth, policies, edge functions, storage if needed)

### Don't Waste time on:
- Security, Performance
- Favicon, logo
- Forget Password page

## Rules:
- Avoid overloading the UI (homepage, admin panel) with too many icons. Most AI-generated code tends to add excessive icons, so keep it simple and use fewer icons.
- Don't leave any placeholders! Populate the site with data and don't add comming soon text.
- Based on project type, all data must be stored in Supabase and create all necessary tables
- No static data in frontend, make sure to use supabase to store them