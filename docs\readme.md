# About me

I own 10 SaaS platforms that I built myself in 10 days.
[See the tips I used to build 1 SaaS per day](./tips.md)

# Goal

My goal is to build a portfolio of 100+ SaaS projects for myself. I don’t expect each app to be bug-free. Each project should be completed within a $100 budget. If you can build more than one, feel free to take on multiple projects. (we can create multiple contracts for each)

The reason for the $50 - $200 budget per project is that I don’t expect perfect SaaS apps without bugs or advanced features.

I only want the basic features that every SaaS should have.
[Here are the features I expect](./features.md)

# How to Proceed:
- Select a project from [this list](./projects.md)
- Read and understand what I expect as the definition of done [here](./definition-of-done.md)
- Let me know which project you’ve selected
- Once we agree, I will send you the offer
- You start working on the project you selected by following [these steps](./how-to-proceed.md)
- After you complete the project as per the definition of done, I will release the payment