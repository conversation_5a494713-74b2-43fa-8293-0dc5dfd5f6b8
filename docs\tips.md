# Choose Your Vibe Coding Platform

Platforms like **lovable.dev** and **augmentcode** can help you build more than 10 features in just 10 minutes using Agent Remote Workspace and parallel task execution(Augment's agents keep working while you're offline[you simply review and merge when read]). If you want to build faster and better, it's worth learning how to use these tools. However, it’s important to understand what you’re doing, because the next step is always debugging. Make sure to learn about context engineering and prompt engineering to help prevent the AI from hallucinating.

- [augment-code](https://www.augmentcode.com/) - My choice (now we just use this to refine it and fix issues and let me auto build the rest for free)
- [lovable.dev](https://lovable.dev) - My choice (First, integrate Supabase, login, and all complex pages like /admin. After that, take the codebase, open it in your VS Code, and continue working with tools like Cursor or Augmentcode.)
- [bolt.new](https://bolt.new)
- [replit.com](https://replit.com)
- [cline.bot](https://cline.bot)
- [roo-code](https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline)
- [cursor.sh](https://cursor.sh)
- [readdy.ai](https://readdy.ai) - My choice (use for 1 quick landing page and then transfer to lovable.dev to continue)
- [same.new](https://same.new)
- [heroui.chat](https://heroui.chat)
- [mgx.dev](https://mgx.dev)
- [web.lmarena.ai](https://web.lmarena.ai)
- [uxpilot.ai](https://uxpilot.ai)
- [app.tempo.new](https://app.tempo.new)
- [chef.convex.dev](https://chef.convex.dev)
- [www.crack.diy](https://www.crack.diy)
- [databutton.com](https://databutton.com)
- [jules.google](https://jules.google)