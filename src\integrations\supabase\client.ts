// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xsiwdckklpslfxppdjhu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhzaXdkY2trbHBzbGZ4cHBkamh1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyODE3OTgsImV4cCI6MjA2ODg1Nzc5OH0.JAhwChLZFHip_31t73thqAwg4-MrsfHS56X8B-pR9hQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});