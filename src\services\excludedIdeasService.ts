import { supabase } from '@/integrations/supabase/client';

export interface ExcludedIdea {
  id: string;
  title: string;
  description?: string;
  category?: string;
  target_user?: string;
  reason?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateExcludedIdeaData {
  title: string;
  description?: string;
  category?: string;
  target_user?: string;
  reason?: string;
}

class ExcludedIdeasService {
  async getAllExcludedIdeas(): Promise<ExcludedIdea[]> {
    const { data, error } = await supabase
      .from('excluded_ideas')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch excluded ideas: ${error.message}`);
    }

    return data || [];
  }

  async getExcludedIdeaTitles(): Promise<string[]> {
    const { data, error } = await supabase
      .from('excluded_ideas')
      .select('title');

    if (error) {
      throw new Error(`Failed to fetch excluded idea titles: ${error.message}`);
    }

    return data?.map(item => item.title) || [];
  }

  async getExcludedIdeasWithDetails(): Promise<{ title: string; description?: string }[]> {
    const { data, error } = await supabase
      .from('excluded_ideas')
      .select('title, description');

    if (error) {
      throw new Error(`Failed to fetch excluded ideas with details: ${error.message}`);
    }

    return data || [];
  }

  async addExcludedIdea(ideaData: CreateExcludedIdeaData): Promise<ExcludedIdea> {
    const { data, error } = await supabase
      .from('excluded_ideas')
      .insert([ideaData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to add excluded idea: ${error.message}`);
    }

    return data;
  }

  async removeExcludedIdea(id: string): Promise<void> {
    const { error } = await supabase
      .from('excluded_ideas')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to remove excluded idea: ${error.message}`);
    }
  }

  async isIdeaExcluded(title: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('excluded_ideas')
      .select('id')
      .eq('title', title)
      .limit(1);

    if (error) {
      throw new Error(`Failed to check if idea is excluded: ${error.message}`);
    }

    return (data?.length || 0) > 0;
  }

  async addMultipleExcludedIdeas(ideas: CreateExcludedIdeaData[]): Promise<ExcludedIdea[]> {
    const { data, error } = await supabase
      .from('excluded_ideas')
      .insert(ideas)
      .select();

    if (error) {
      throw new Error(`Failed to add multiple excluded ideas: ${error.message}`);
    }

    return data || [];
  }

  // Check if any ideas in a list are similar to excluded ones
  async filterOutExcludedIdeas<T extends { title: string; description: string; category: string; target_user: string }>(ideas: T[]): Promise<T[]> {
    const excludedTitles = await this.getExcludedIdeaTitles();
    
    if (excludedTitles.length === 0) {
      return ideas;
    }

    // Filter out ideas that match excluded titles (case-insensitive)
    const excludedTitlesLower = excludedTitles.map(title => title.toLowerCase());
    
    return ideas.filter(idea => {
      const ideaTitleLower = idea.title.toLowerCase();
      return !excludedTitlesLower.includes(ideaTitleLower);
    });
  }
}

export const excludedIdeasService = new ExcludedIdeasService();
