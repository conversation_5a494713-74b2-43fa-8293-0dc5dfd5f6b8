import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MermaidDiagram } from '@/components/MermaidDiagram';
import { AlertCircle, Eye, Code, Save } from 'lucide-react';

interface MermaidEditorProps {
  diagramCode: string;
  onSave: (diagramCode: string) => void;
  onCancel: () => void;
}

export function MermaidEditor({ diagramCode, onSave, onCancel }: MermaidEditorProps) {
  const [localDiagramCode, setLocalDiagramCode] = useState(diagramCode);
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setLocalDiagramCode(diagramCode);
    setHasChanges(false);
  }, [diagramCode]);

  const handleDiagramChange = (value: string) => {
    setLocalDiagramCode(value);
    setHasChanges(value !== diagramCode);
  };

  const handleSave = () => {
    onSave(localDiagramCode);
    setHasChanges(false);
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        setLocalDiagramCode(diagramCode);
        setHasChanges(false);
        onCancel();
      }
    } else {
      onCancel();
    }
  };

  const addSampleTemplate = () => {
    const template = `graph TD
    subgraph User Flow
        A[User visits homepage] --> B{Logged In?}
        B -->|No| C[Show signup/login]
        C --> D[User creates account]
        D --> E[Redirect to dashboard]
        B -->|Yes| E
        E --> F[User accesses main features]
        F --> G[User manages subscription]
    end

    subgraph Admin Flow
        H[Admin logs in] --> I[View admin panel]
        I --> J[Manage users]
        J --> K[View analytics]
        K --> L[Manage subscriptions]
    end

    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style H fill:#fff3e0`;

    setLocalDiagramCode(template);
    setHasChanges(template !== diagramCode);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Edit Mermaid Flow Diagram</h3>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={addSampleTemplate}
          >
            Load Template
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            disabled={!hasChanges}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      {hasChanges && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You have unsaved changes to the diagram.
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'edit' | 'preview')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="edit" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            Edit Code
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="edit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Mermaid Diagram Code</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={localDiagramCode}
                onChange={(e) => handleDiagramChange(e.target.value)}
                placeholder="Enter your Mermaid diagram code here..."
                className="min-h-[400px] font-mono text-sm"
              />
              <div className="mt-2 text-xs text-gray-500">
                <p>Tip: Use Mermaid syntax. Start with "graph TD" or "flowchart TD" for flowcharts.</p>
                <p>Example: graph TD; A[Start] --&gt; B[Process] --&gt; C[End]</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Diagram Preview</CardTitle>
            </CardHeader>
            <CardContent>
              {localDiagramCode.trim() ? (
                <MermaidDiagram 
                  diagram={localDiagramCode} 
                  title="Flow Diagram Preview" 
                />
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <p>No diagram code to preview</p>
                  <p className="text-xs mt-1">Switch to Edit tab to add diagram code</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
