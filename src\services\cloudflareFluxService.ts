import { geminiService } from './geminiService';

interface FluxResponse {
  result: {
    image: string; // Base64 encoded image
  };
  success: boolean;
  errors: any[];
  messages: any[];
}

interface ImageRequest {
  prompt: string;
  steps?: number;
  model?: string;
}

class CloudflareFluxService {
  private readonly supabaseUrl: string;
  private readonly supabaseAnonKey: string;
  private readonly maxRetries = 3;
  private readonly retryDelay = 2000; // 2 seconds

  constructor() {
    this.supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    this.supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!this.supabaseUrl || !this.supabaseAnonKey) {
      throw new Error('Supabase credentials are required. Please check your environment variables');
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private generateLandingPagePrompt(projectName: string, projectDescription: string): string {
    return `Complete full-length landing page mockup for SaaS application "${projectName}" in ultra HD quality.

    Project: ${projectDescription}

    Show ENTIRE page from header to footer including:
    - Header with navigation bar and logo
    - Hero section with compelling headline and CTA button
    - Features section with benefit cards and icons
    - Pricing or testimonials section
    - Footer with links and contact info

    Style: Modern, clean, professional SaaS design, high resolution, detailed UI elements, contemporary color scheme, desktop view, complete scrollable page layout from top to bottom, ultra HD quality`;
  }

  // New method that uses Gemini to generate the prompt
  async generateLandingPageImageWithAI(projectName: string, projectDescription: string, projectContent: any, model: string = '@cf/black-forest-labs/flux-1-schnell'): Promise<string> {
    // Use Gemini to generate a custom prompt based on project content
    let prompt = await geminiService.generateLandingPagePrompt(projectName, projectDescription, projectContent);

    // Ensure prompt doesn't exceed Cloudflare's 2048 character limit
    if (prompt.length > 2048) {
      console.warn(`Prompt too long (${prompt.length} chars), truncating to 2048 chars`);
      prompt = prompt.substring(0, 2045) + '...';
    }

    return this.generateImageFromPrompt(prompt, projectName, model);
  }

  // Legacy method (kept for backward compatibility)
  async generateLandingPageImage(projectName: string, projectDescription: string, model: string = '@cf/black-forest-labs/flux-1-schnell'): Promise<string> {
    const prompt = this.generateLandingPagePrompt(projectName, projectDescription);
    return this.generateImageFromPrompt(prompt, projectName, model);
  }

  // Common method to generate image from prompt
  private async generateImageFromPrompt(prompt: string, projectName: string, model: string): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`Attempting to generate landing page image for "${projectName}" using ${model} (attempt ${attempt}/${this.maxRetries})`);

        const requestBody: ImageRequest = {
          prompt,
          steps: 6, // Higher quality with more steps
          model
        };

        // Call Supabase Edge Function instead of Cloudflare API directly
        const response = await fetch(`${this.supabaseUrl}/functions/v1/generate-landing-image`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.supabaseAnonKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Supabase Edge Function error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(`Image generation failed: ${data.error || 'Unknown error'}`);
        }

        if (!data.imageUrl) {
          throw new Error('No image URL returned from image generation service');
        }

        console.log('Successfully generated landing page image');

        // Return the public URL
        return data.imageUrl;

      } catch (error) {
        lastError = error as Error;
        console.error(`Attempt ${attempt} failed:`, error);

        if (attempt < this.maxRetries) {
          console.log(`Retrying in ${this.retryDelay / 1000} seconds...`);
          await this.delay(this.retryDelay);
        }
      }
    }

    throw new Error(`Failed to generate landing page image after ${this.maxRetries} attempts. Last error: ${lastError?.message}`);
  }

  // Test method to verify API connectivity
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.generateLandingPageImage(
        "Test App",
        "A simple test application to verify API connectivity"
      );
      return response.startsWith('http');
    } catch (error) {
      console.error('Image generation service test failed:', error);
      return false;
    }
  }
}

export const cloudflareFluxService = new CloudflareFluxService();
export type { FluxResponse, ImageRequest };
