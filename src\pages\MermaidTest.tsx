import React from 'react';
import { MermaidDiagram } from '@/components/MermaidDiagram';

const testDiagram = `flowchart TD
    subgraph User Flow
        direction LR
        A[Homepage] --> B{Sign Up / Login}
        B --> C[Subscribe to Pro Plan via Stripe]
        C --> D[Access Dashboard]
        D --> E[Add/Manage Team Members]
        E --> F[View Coordinated Time Zones]
        D --> G[Manage Profile & Billing]
    end

    subgraph Admin Flow
        direction LR
        H[Admin Login] --> I[View User List]
        I --> J{Select User}
        J --> K[Edit User / Toggle Subscription]
    end

    style A fill:#e3f2fd,stroke:#333
    style B fill:#e3f2fd,stroke:#333
    style C fill:#f3e5f5,stroke:#333
    style D fill:#e8f5e9,stroke:#333
    style E fill:#e8f5e9,stroke:#333
    style F fill:#e8f5e9,stroke:#333
    style G fill:#e8f5e9,stroke:#333

    style H fill:#fff3e0,stroke:#333
    style I fill:#fff3e0,stroke:#333
    style J fill:#fff3e0,stroke:#333
    style K fill:#fff3e0,stroke:#333`;

export default function MermaidTest() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Mermaid Diagram Test</h1>
      
      <div className="space-y-8">
        <MermaidDiagram 
          diagram={testDiagram}
          title="Platform Flow Diagram Test"
        />
        
        <MermaidDiagram 
          diagram="flowchart TD\n    A[Start] --> B[Process]\n    B --> C[End]"
          title="Simple Test Diagram"
        />
      </div>
    </div>
  );
}
