import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BookOpen, ExternalLink, Home, LogOut } from "lucide-react";

const PrivateTips = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const isAuthenticated = localStorage.getItem("tipsAuth") === "true";
    if (!isAuthenticated) {
      navigate("/tips-login");
    }
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem("tipsAuth");
    navigate("/tips-login");
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="p-4 border-b border-border bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center gap-3">
            <BookOpen className="h-8 w-8 text-purple-600" />
            <h1 className="text-3xl font-bold text-gray-800">Private Tips</h1>
          </div>
          <div className="space-x-4">
            <Button variant="outline" onClick={() => navigate("/")} className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Home
            </Button>
            <Button variant="outline" onClick={handleLogout} className="flex items-center gap-2">
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto p-8">
        <div className="space-y-8">
          {/* Introduction */}
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border border-purple-200">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">Choose Your Vibe Coding Platform</h2>
            <p className="text-gray-700 leading-relaxed text-lg">
              Platforms like <strong>lovable.dev</strong> and <strong>augmentcode</strong> can help you build more than 10 features in just 10 minutes using Agent Remote Workspace and parallel task execution(Augment's agents keep working while you're offline[you simply review and merge when needed]). If you want to build faster and better, it's worth learning how to use these tools. However, it's important to understand what you're doing, because the next step is always debugging. Make sure to learn about context engineering and prompt engineering to help prevent the AI from hallucinating.
            </p>
            <p className="text-gray-700 leading-relaxed text-lg">
              <strong>If you like using Cursor or Windsurf, you should learn Augment Code. It can help you get a job much faster. (But the choice is yours, feel free to use whatever you want. We've had success building quickly with tools like Augment Code, and we provide tips for it. However, you are free to use any AI-assisted workflow (e.g., Cursor, GitHub Copilot Chat, Cline, etc etc) as long as you meet the technical requirements and deliver what I expect[Using best principle etc])</strong>
            </p>
          </div>

          {/* How to build UI under 5 mins */}
          <div className="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-lg border border-blue-200">
            <h3 className="text-2xl font-semibold mb-6 text-gray-800">
              How to Build UI in Under 5 Minutes
            </h3>
            <div className="bg-white p-4 rounded-lg border border-blue-200 shadow-sm">
              <div className="space-y-3 text-gray-700 leading-relaxed">
                <p>
                  After you select a project, copy the prompt next to the "Homepage" section, paste it into{' '}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium"
                    asChild
                  >
                    <a href="https://lovable.dev" target="_blank" rel="noopener noreferrer">lovable.dev</a>
                  </Button>
                  {' '}and it will generate the complete UI for you. Note: Sometime lovable.dev tends to add a lot of stuff, you may require to clean some of it in the UI. But overall, it should generate all the necessary pages/components also such as /admin, /login page and others. Then next is to link those pages accordingly. Sometime you may need to take 10-20min to review and refine where necessary.
                </p>
                <p className="text-sm text-blue-800 bg-blue-50 p-3 rounded border border-blue-200">
                  <strong>Pro Tip:</strong> If you run out of credits, there are multiple ways to continue.
                  Apply your knowledge and creativity to find solutions!
                </p>
              </div>
            </div>
          </div>

          {/* How to continue development after UI */}
          <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-lg border border-purple-200">
            <h3 className="text-2xl font-semibold mb-6 text-gray-800">
              How to Continue Development After UI is Built
            </h3>

            <div className="space-y-6">
              {/* Option 1 */}
              <div className="bg-white p-4 rounded-lg border border-purple-200 shadow-sm">
                <h4 className="text-lg font-semibold mb-3 text-purple-800 flex items-center gap-2">
                  <span className="w-6 h-6 rounded-full bg-purple-600 text-white text-sm flex items-center justify-center font-bold">1</span>
                  Continue in Lovable.dev
                </h4>
                <div className="space-y-3 text-gray-700 leading-relaxed">
                  <p>
                    Connect your project to GitHub and remix it using another account. Then connect Supabase and
                    write your prompt to complete the project automatically. This step handles the heavy development
                    work and usually takes an additional 5 minutes.
                  </p>
                  <div className="bg-yellow-50 p-3 rounded border border-yellow-200">
                    <p className="text-sm text-yellow-800">
                      <strong>Smart Approach:</strong> If you apply your knowledge, you'll understand how to
                      complete the entire project in lovable.dev. However, I recommend moving to Augment Code
                      for the remaining development.
                    </p>
                  </div>
                </div>
              </div>

              {/* Option 2 */}
              <div className="bg-white p-4 rounded-lg border border-purple-200 shadow-sm">
                <h4 className="text-lg font-semibold mb-3 text-purple-800 flex items-center gap-2">
                  <span className="w-6 h-6 rounded-full bg-purple-600 text-white text-sm flex items-center justify-center font-bold">2</span>
                  Transfer to VS Code + Augment Code (Recommended)
                </h4>
                <div className="space-y-3 text-gray-700 leading-relaxed">
                  <p>
                    Connect to GitHub and transfer the project to VS Code. Install the{' '}
                    <Button
                      variant="link"
                      className="p-0 h-auto text-purple-600 hover:text-purple-800 font-medium"
                      asChild
                    >
                      <a href="https://www.augmentcode.com/" target="_blank" rel="noopener noreferrer">Augment Code extension</a>
                    </Button>
                    {' '}for faster development. Avoid cursor, cline, roo-code, winsurf, etc., as they are inferior.
                    Always choose Augment Code.
                  </p>
                  <p>
                    If you don't know how to use Augment Code, learn it - it's worth the investment. With Augment Code,
                    you can use remote workspace capabilities.
                  </p>
                  <div className="bg-green-50 p-3 rounded border border-green-200">
                    <p className="text-sm text-green-800">
                      <strong>Best Practice:</strong> Generate all required .md files and Mermaid diagrams if needed.
                      Take the full project details and paste them into an .md file in VS Code. Ask it to create a task
                      list and follow the checklist. The entire project can be completed within 20 minutes.
                    </p>
                  </div>
                  <div className="bg-orange-50 p-3 rounded border border-orange-200">
                    <p className="text-sm text-orange-800">
                      <strong>Final Step:</strong> After completion comes debugging. Use your knowledge and skills
                      to fix issues until the project is fully functional.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Development Tips */}
          <div className="bg-gradient-to-br from-indigo-50 to-cyan-50 p-6 rounded-lg border border-indigo-200">
            <h3 className="text-xl font-semibold mb-4 text-gray-800">
              Additional Development Tips
            </h3>
            <p className="text-sm text-gray-600 mb-4 italic">
              (Not my requirement, but for your own good - your choice)
            </p>
            <ul className="space-y-3 text-gray-700">
              <li className="flex items-start gap-3">
                <span>Create a Mermaid diagram to show the project flow</span>
              </li>
              <li className="flex items-start gap-3">
                <span>Add relevant .md files to help track the work and avoid AI hallucinations</span>
              </li>
              <li className="flex items-start gap-3">
                <span>Use useful MCPs like sequential-thinking, Supabase, Task Management, and others where needed</span>
              </li>
              <li className="flex items-start gap-3">
                <span>Use Context7 MCP to fetch official documentation to help you build accurately or let the AI search online always!</span>
              </li>
            </ul>
          </div>

          {/* Recommended Platforms */}
          <div>
            <h3 className="text-2xl font-semibold mb-6 text-gray-800">Recommended Platforms</h3>
            <div className="grid gap-4">
              <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center gap-4">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div>
                    <span className="font-semibold text-green-800 text-lg">augment-code</span>
                    <Badge variant="outline" className="ml-3 bg-green-100 text-green-700 border-green-300">My choice</Badge>

                  </div>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <a href="https://www.augmentcode.com/" target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    Visit
                  </a>
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center gap-4">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <span className="font-semibold text-blue-800 text-lg">lovable.dev</span>
                    <Badge variant="outline" className="ml-3 bg-blue-100 text-blue-700 border-blue-300">My choice</Badge>
                 </div>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <a href="https://lovable.dev" target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    Visit
                  </a>
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-orange-50 border border-orange-200 rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center gap-4">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <div>
                    <span className="font-semibold text-orange-800 text-lg">readdy.ai</span>
                    <Badge variant="outline" className="ml-3 bg-orange-100 text-orange-700 border-orange-300">My choice</Badge>
                    <p className="text-sm text-orange-600 mt-1">Use for 1 quick landing page and then transfer to lovable.dev to continue</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <a href="https://readdy.ai" target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    Visit
                  </a>
                </Button>
              </div>
            </div>
          </div>

          {/* Other Platforms */}
          <div>
            <h3 className="text-2xl font-semibold mb-6 text-gray-800">Other Platforms</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {[
                { name: "bolt.new", url: "https://bolt.new" },
                { name: "replit.com", url: "https://replit.com" },
                { name: "cline.bot", url: "https://cline.bot" },
                { name: "roo-code", url: "https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline" },
                { name: "cursor.sh", url: "https://cursor.sh" },
                { name: "same.new", url: "https://same.new" },
                { name: "heroui.chat", url: "https://heroui.chat" },
                { name: "mgx.dev", url: "https://mgx.dev" },
                { name: "web.lmarena.ai", url: "https://web.lmarena.ai" },
                { name: "uxpilot.ai", url: "https://uxpilot.ai" },
                { name: "app.tempo.new", url: "https://app.tempo.new" },
                { name: "chef.convex.dev", url: "https://chef.convex.dev" },
                { name: "www.crack.diy", url: "https://www.crack.diy" },
                { name: "databutton.com", url: "https://databutton.com" },
                { name: "jules.google", url: "https://jules.google" }
              ].map((platform) => (
                <Button key={platform.name} variant="outline" size="sm" asChild className="justify-between h-auto p-3">
                  <a href={platform.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                    <span className="text-sm font-medium">{platform.name}</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                </Button>
              ))}
            </div>
          </div>

     

        </div>
      </div>
    </div>
  );
};

export default PrivateTips;
