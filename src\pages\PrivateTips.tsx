import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BookOpen, ExternalLink, Home, LogOut } from "lucide-react";

const PrivateTips = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const isAuthenticated = localStorage.getItem("tipsAuth") === "true";
    if (!isAuthenticated) {
      navigate("/tips-login");
    }
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem("tipsAuth");
    navigate("/tips-login");
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="p-4 border-b border-border bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center gap-3">
            <BookOpen className="h-8 w-8 text-purple-600" />
            <h1 className="text-3xl font-bold text-gray-800">Private Tips</h1>
          </div>
          <div className="space-x-4">
            <Button variant="outline" onClick={() => navigate("/")} className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Home
            </Button>
            <Button variant="outline" onClick={handleLogout} className="flex items-center gap-2">
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto p-8">
        <div className="space-y-8">
          {/* Introduction */}
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border border-purple-200">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">Welcome to the Vibe Coder Guide</h2>
            <p className="text-gray-700 leading-relaxed text-lg mb-4">
              This page isn't a strict rulebook; it's a collection of tips and workflows we've found to be incredibly fast.
              The goal is to build a functional SaaS in hours, not weeks. We trust your expertise. Use these methods as
              a starting point, adapt them, or use your own workflow if it achieves the same rapid results.
            </p>

            <div className="bg-white p-5 rounded-lg border border-purple-200 shadow-sm mt-6">
              <h3 className="text-xl font-semibold mb-4 text-purple-800">Our Core Philosophy: The AI-First Mindset</h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                The key to this process is treating AI as your co-pilot. You aren't just writing code; you are expertly
                guiding a powerful system to do the heavy lifting for you. This requires strong skills in:
              </p>
              <ul className="space-y-2 text-gray-700 ml-4">
                <li className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Prompt Engineering:</strong> Clearly defining what you want the AI to build.</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Context Engineering:</strong> Providing the AI with the right information (like our project specs) to avoid hallucinations.</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></span>
                  <span><strong>Expert Debugging:</strong> Quickly identifying and fixing the inevitable mistakes the AI will make. This is where your skill as a developer truly shines.</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Example Workflow */}
          <div className="bg-gradient-to-br from-blue-50 to-cyan-50 p-6 rounded-lg border border-blue-200">
            <h3 className="text-2xl font-semibold mb-6 text-gray-800">
              An Example Workflow: From Idea to SaaS in Under 2 Hours
            </h3>
            <p className="text-gray-700 leading-relaxed mb-6">
              Here's one proven path to building these projects at speed.
            </p>

            {/* Step 1 */}
            <div className="bg-white p-5 rounded-lg border border-blue-200 shadow-sm mb-6">
              <h4 className="text-lg font-semibold mb-3 text-blue-800 flex items-center gap-2">
                <span className="w-6 h-6 rounded-full bg-blue-600 text-white text-sm flex items-center justify-center font-bold">1</span>
                UI Generation (The First 15-20 Minutes)
              </h4>
              <div className="space-y-3 text-gray-700 leading-relaxed">
                <p>
                  Your first task is to generate the static UI for all required pages. Our project specs include a detailed
                  prompt designed for this. You have complete flexibility here:
                </p>
                <p>
                  There are many great frontend AI platforms. Feel free to use what you are most familiar with. Some good options include:
                </p>
                <ul className="ml-6 space-y-1">
                  <li className="flex items-center gap-2">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
                    <Button variant="link" className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium" asChild>
                      <a href="https://lovable.dev" target="_blank" rel="noopener noreferrer">lovable.dev</a>
                    </Button>
                    <span className="text-sm text-gray-600">(Our prompts are well-optimized for this)</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
                    <Button variant="link" className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium" asChild>
                      <a href="https://readdy.ai" target="_blank" rel="noopener noreferrer">readdy.ai</a>
                    </Button>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
                    <Button variant="link" className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium" asChild>
                      <a href="https://uxpilot.ai" target="_blank" rel="noopener noreferrer">uxpilot.ai</a>
                    </Button>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
                    <Button variant="link" className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium" asChild>
                      <a href="https://stitch.withgoogle.com" target="_blank" rel="noopener noreferrer">stitch.withgoogle.com</a>
                    </Button>
                  </li>
                </ul>
                <p className="text-sm text-blue-800 bg-blue-50 p-3 rounded border border-blue-200">
                  <strong>Goal:</strong> Get a solid, visually aligned set of static pages (Homepage, Dashboard, Login, etc.) ready for the next step.
                </p>
              </div>
            </div>

            {/* Step 2 */}
            <div className="bg-white p-5 rounded-lg border border-blue-200 shadow-sm mb-6">
              <h4 className="text-lg font-semibold mb-3 text-blue-800 flex items-center gap-2">
                <span className="w-6 h-6 rounded-full bg-blue-600 text-white text-sm flex items-center justify-center font-bold">2</span>
                Backend & Logic Integration (The Heavy Lifting)
              </h4>
              <div className="space-y-3 text-gray-700 leading-relaxed">
                <p>
                  Once the UI is approved, it's time to bring it to life. This means connecting to Supabase, building
                  the database, and implementing all user features.
                </p>
                <p>
                  For this phase, a powerful AI assistant inside your IDE is essential. Our go-to choice is{' '}
                  <Button variant="link" className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium" asChild>
                    <a href="https://www.augmentcode.com/" target="_blank" rel="noopener noreferrer">Augment Code</a>
                  </Button>
                  {' '}in VS Code, primarily for its "Agent Remote Workspace" feature that can execute complex tasks.
                  However, if you are faster with Cursor, GitHub Copilot Chat, or another tool, use it. The end result is what matters.
                </p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="bg-white p-5 rounded-lg border border-blue-200 shadow-sm">
              <h4 className="text-lg font-semibold mb-3 text-blue-800 flex items-center gap-2">
                <span className="w-6 h-6 rounded-full bg-blue-600 text-white text-sm flex items-center justify-center font-bold">3</span>
                Finalization & Debugging
              </h4>
              <div className="space-y-3 text-gray-700 leading-relaxed">
                <p>
                  This is the last mile. You'll integrate Stripe, build the admin panel, and hunt down any remaining bugs.
                  No AI-generated project is perfect on the first try. Your ability to quickly test, identify issues,
                  and deploy fixes is crucial for finishing the project.
                </p>
              </div>
            </div>
          </div>

          {/* Key Principles */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg border border-green-200">
            <h3 className="text-2xl font-semibold mb-6 text-gray-800">
              Key Principles for Speed & Quality
            </h3>
            <p className="text-gray-700 leading-relaxed mb-6">
              These are not requirements, but best practices that prevent errors and speed up development:
            </p>

            <div className="grid gap-4">
              <div className="bg-white p-4 rounded-lg border border-green-200 shadow-sm">
                <h4 className="font-semibold text-green-800 mb-2">Plan Your Flow</h4>
                <p className="text-gray-700 text-sm">
                  Before you start, consider creating a quick Mermaid diagram to visualize the user and data flow.
                  This helps you give the AI clearer instructions.
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg border border-green-200 shadow-sm">
                <h4 className="font-semibold text-green-800 mb-2">Stay Organized</h4>
                <p className="text-gray-700 text-sm">
                  Keep the full project details in an .md file in your repository. You can use this to create
                  checklists and feed context to the AI, reducing hallucinations.
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg border border-green-200 shadow-sm">
                <h4 className="font-semibold text-green-800 mb-2">Trust But Verify</h4>
                <p className="text-gray-700 text-sm">
                  Use official documentation. An AI assistant that can browse the web to check the latest
                  Supabase or Stripe docs is invaluable for accuracy.
                </p>
              </div>
            </div>
          </div>

          {/* Recommended Platforms */}
          <div>
            <h3 className="text-2xl font-semibold mb-6 text-gray-800">Recommended Platforms</h3>
            <div className="grid gap-4">
                <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-center gap-4">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                    <span className="font-semibold text-green-800 text-lg">augment-code</span>
                    <Badge variant="outline" className="ml-3 bg-green-100 text-green-700 border-green-300">My choice</Badge>

                    </div>
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <a href="https://www.augmentcode.com/" target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4" />
                      Visit
                    </a>
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-center gap-4">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <div>
                      <span className="font-semibold text-blue-800 text-lg">lovable.dev</span>
                    <Badge variant="outline" className="ml-3 bg-blue-100 text-blue-700 border-blue-300">My choice</Badge>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <a href="https://lovable.dev" target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4" />
                      Visit
                    </a>
                  </Button>
                </div>
              
              <div className="flex items-center justify-between p-4 bg-orange-50 border border-orange-200 rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center gap-4">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <div>
                    <span className="font-semibold text-orange-800 text-lg">readdy.ai</span>
                    <Badge variant="outline" className="ml-3 bg-orange-100 text-orange-700 border-orange-300">My choice</Badge>
                    <p className="text-sm text-orange-600 mt-1">Use for 1 quick landing page and then transfer to lovable.dev to continue</p>
              </div>
            </div>
                <Button variant="ghost" size="sm" asChild>
                  <a href="https://readdy.ai" target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    Visit
                    </a>
                  </Button>
              </div>
              </div>
            </div>

          {/* Other Platforms */}
            <div>
            <h3 className="text-2xl font-semibold mb-6 text-gray-800">Other Platforms</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {[
                { name: "bolt.new", url: "https://bolt.new" },
                  { name: "replit.com", url: "https://replit.com" },
                  { name: "cline.bot", url: "https://cline.bot" },
                { name: "roo-code", url: "https://marketplace.visualstudio.com/items?itemName=RooVeterinaryInc.roo-cline" },
                { name: "cursor.sh", url: "https://cursor.sh" },
                { name: "same.new", url: "https://same.new" },
                { name: "heroui.chat", url: "https://heroui.chat" },
                { name: "mgx.dev", url: "https://mgx.dev" },
                { name: "web.lmarena.ai", url: "https://web.lmarena.ai" },
                { name: "uxpilot.ai", url: "https://uxpilot.ai" },
                { name: "app.tempo.new", url: "https://app.tempo.new" },
                { name: "chef.convex.dev", url: "https://chef.convex.dev" },
                { name: "www.crack.diy", url: "https://www.crack.diy" },
                { name: "databutton.com", url: "https://databutton.com" },
                { name: "jules.google", url: "https://jules.google" }
                ].map((platform) => (
                  <Button key={platform.name} variant="outline" size="sm" asChild className="justify-between h-auto p-3">
                    <a href={platform.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                      <span className="text-sm font-medium">{platform.name}</span>
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  </Button>
                ))}
              </div>
            </div>

     

        </div>
      </div>
    </div>
  );
};

export default PrivateTips;
