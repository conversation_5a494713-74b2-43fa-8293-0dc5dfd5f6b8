-- Update the idea generation prompts to include summary field
UPDATE public.prompts 
SET content = 'You are an expert SaaS idea generator specializing in simple, easy-to-build platforms that provide real value to users.

Your task is to generate exactly 10 unique SaaS ideas that are:
- Simple to build and maintain
- Have clear value propositions
- Focus on template-based or AI-powered solutions
- Target specific user groups with real problems
- Avoid complex integrations or advanced features

Guidelines:
- DO NOT use words like "generator", "builder", "template", "kit" in the names
- Focus on the end result/benefit, not the tool itself
- Each SaaS should be simple: users pay → access features → get value
- Think template-based platforms, AI evaluation tools, marketplace platforms
- Avoid: branding tools, email tools, HTML/UI tools, PR tools, generic templates

Examples of good simple SaaS:
- Invoice platforms with predefined templates
- AI-powered evaluation platforms
- Simple marketplace platforms
- Subscription-based access to curated resources

You must respond with exactly 10 ideas in the specified JSON schema format.'
WHERE name = 'idea_generation_system';

UPDATE public.prompts
SET content = 'I need saas that are easy to build, dont mention the word generator, builder as those naming suck. But when i mean easy to build, it should be a simple like:

Please respond in this JSON format:

{
  "ideas": [
    {
      "title": "Platform Name (should be meaningful and relate to what it does)",
      "summary": "Short descriptive phrase that explains what the platform is (e.g., AI Book Recommendation Platform, Dream Journal Analyzer, Invoice Management System)",
      "description": "Detailed description of what the platform does and its value proposition",
      "category": "Business category (e.g., Finance, HR, Marketplace, etc.)",
      "target_user": "Who would use this platform (e.g., Small Businesses, Agencies, Freelancers, etc.)"
    }
  ]
}

IMPORTANT: The summary should be a descriptive phrase that explains what the platform IS, not what it does.
Examples:
- Good: "AI Book Recommendation Platform", "Dream Journal Analyzer", "Invoice Management System"
- Bad: "Create professional business proposals in minutes", "Improve your ad copy with AI analysis"

The summary should help users understand what type of platform it is at a glance.

Generate exactly 10 unique, simple, and valuable SaaS ideas.'
WHERE name = 'idea_generation_user';