import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { ideaGenerationService, SaasIdea } from '@/services/ideaGenerationService';
import { toast } from '@/hooks/use-toast';
import { defaultProjectContent } from '@/types/project';

export function useIdeaGeneration() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedIdeas, setGeneratedIdeas] = useState<SaasIdea[]>([]);

  const fetchExistingProjectNames = async (): Promise<string[]> => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('name');

      if (error) {
        console.error('Error fetching project names:', error);
        return [];
      }

      return data?.map(project => project.name) || [];
    } catch (error) {
      console.error('Error fetching project names:', error);
      return [];
    }
  };

  const generateIdeas = async (criticalNotes?: string) => {
    setIsGenerating(true);
    setIsModalOpen(true);
    setGeneratedIdeas([]);

    try {
      // Fetch existing project names to avoid duplicates
      const existingProjects = await fetchExistingProjectNames();

      // Generate new ideas with optional critical notes
      const response = await ideaGenerationService.generateIdeas(existingProjects, criticalNotes);

      setGeneratedIdeas(response.ideas);

      toast({
        title: "Ideas Generated!",
        description: `Successfully generated ${response.ideas.length} SaaS ideas.`,
      });
    } catch (error) {
      console.error('Error generating ideas:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate ideas. Please try again.",
        variant: "destructive",
      });
      setIsModalOpen(false);
    } finally {
      setIsGenerating(false);
    }
  };

  const saveSelectedIdeas = async (selectedIdeas: SaasIdea[]) => {
    if (selectedIdeas.length === 0) {
      toast({
        title: "No Ideas Selected",
        description: "Please select at least one idea to save.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Convert SaasIdea to Project format
      const projectsToInsert = selectedIdeas.map(idea => ({
        name: `${idea.title} - ${idea.summary}`,
        description: idea.description,
        category: idea.category,
        target_user: idea.target_user,
        status: 'Unclaimed',
        claimed_by: null,
        content: defaultProjectContent,
        pricing_tier: 100, // Default to $100 tier
      }));

      const { error } = await supabase
        .from('projects')
        .insert(projectsToInsert);

      if (error) throw error;

      toast({
        title: "Ideas Saved!",
        description: `Successfully saved ${selectedIdeas.length} idea${selectedIdeas.length > 1 ? 's' : ''} to the database.`,
      });

      setIsModalOpen(false);
      setGeneratedIdeas([]);
      
      // Return true to indicate success so parent can refresh
      return true;
    } catch (error) {
      console.error('Error saving ideas:', error);
      toast({
        title: "Save Failed",
        description: error instanceof Error ? error.message : "Failed to save ideas. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setGeneratedIdeas([]);
  };

  return {
    isModalOpen,
    isGenerating,
    generatedIdeas,
    generateIdeas,
    saveSelectedIdeas,
    closeModal,
  };
}

