-- Add pricing_tier column to projects table
ALTER TABLE public.projects 
ADD COLUMN pricing_tier INTEGER DEFAULT 100;

-- Add check constraint to ensure only valid pricing tiers
ALTER TABLE public.projects 
ADD CONSTRAINT pricing_tier_check 
CHECK (pricing_tier IN (50, 100, 200));

-- Update existing projects without pricing to default $100
UPDATE public.projects 
SET pricing_tier = 100 
WHERE pricing_tier IS NULL;

-- Add index for better performance when filtering by pricing tier
CREATE INDEX idx_projects_pricing_tier ON public.projects(pricing_tier);
