in homepage, only display projects that has already been generated by AI please.

---------------------

in homepage, i want to seperate some saas idea with prices like $50, $100, $200. Then adjust the backend also, so when i click on btn 'generate', it should now estimate whether its a $50, $100 or $200. If ever a data does not have any pricing yet, by default, set it in the $100 tab category. Btw for ui, use tab in the frontend.

-----------------------

in the /admin also, i want to seperate those saas idea that has already been generated with AI vs one which only has title and not proceessed yet by AI. I am doing that so i can know which one i need to process and which one is not yet processed by us.

-----------------------

in the ai generated tab in /admin, there is no need to show the preview content at all. Just make a link to the real project instead so i can just click and it redirect me to the project.

--------------------
- in /admin, add a menu called 'Generate Idea' on the header. When clicked, it should simply generate 10 ideas for me and pass this exact prompt:
"I need saas that are easy to build, dont mention the word generator, builder as those naming suck. But when i mean easy to build, it should be a simple like:
- Write Invoice Platform - Create invoices, manage clients, track tasks, and run your entire business from one clean, powerful platform.
This write invoice platform simply as a section for invoice where people we have a set of predefined templates and the user simply need to choose from it and click on generate to download. So the idea here is, its a simple SaaS where people just pay a package and then can access templates to use. So as admin, we simply add templates for user to use. Simple as that.

- Expert-Recruiters - This is a ai cv evaluation platform where agency register, and upload bulk cvs for analysis, and it get evalated by a set of companies he is managing automatically. The user has a specific /dashboard for that. There are 3 packages where user simply select and based on the subscription plan, he can uploaded a specific numbers of cvs monthly and bulk analyse and evalation by AI.

- EpicFreelancers - This is a platform where user register as seller or buyer to buy diffferent services. The category is simple, we have service, saas, digital product. Here its simple because as a seller, they have a dashboard where they simple choose their category and what they are selling or offering and the design changes accordingly in front-end to display, simple as that.

So basically the idea here is to give me a list of similar projects that i can develop.

And please dont give app like Saas template, branding kits, email tools, html/ui tools, PR tool.

And here is a list of my existing saas you should not re-include:
{fetch all my saas projects names from my DB and provide it to gemini API here}
"

And please refer to how gemini AI is using the schema and do same, do not rewrite, but reuse same files please so its more eaiser to maintain. break the files into smaller if needed please.  i want nice and clean maintainable code and not in a single file code please. refactor previous stuff if required. The output should only be the title of the saas idea and its small description as usually like we already have for the pending one. But once generated, just display it to me in a modal first, then i will be able to select,, deslect which one i want to add in the DB, so only after i select and click on save, then it should save to the db and show in the pending tab in /admin. Do not make it related to the 'generate' btn. This task is for generating saas idea only and not the whole other details.
---------------


// To run this code you need to install the following dependencies:
// npm install @google/genai mime
// npm install -D @types/node

import {
  GoogleGenAI,
  Type,
} from '@google/genai';

async function main() {
  const ai = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY,
  });
  const config = {
    thinkingConfig: {
      thinkingBudget: -1,
    },
    responseMimeType: 'application/json',
    responseSchema: {
      type: Type.OBJECT,
      required: ["dd"],
      properties: {
        dd: {
          type: Type.STRING,
        },
      },
    },
    systemInstruction: [
        {
          text: `system prompt hereeeeeeee`,
        }
    ],
  };
  const model = 'gemini-2.5-pro';
  const contents = [
    {
      role: 'user',
      parts: [
        {
          text: `user promp thereeeeeee`,
        },
      ],
    },
  ];

  const response = await ai.models.generateContentStream({
    model,
    config,
    contents,
  });
  let fileIndex = 0;
  for await (const chunk of response) {
    console.log(chunk.text);
  }
}

main();


------------

When ideas get generated in /admin, i want them to be also editable directly in the modal here:
"Generated SaaS Ideas
Select All
0 of 10 selected
Cancel
Save Selected (0)

RoleReady
HR
Hiring Managers
Provides a library of professionally written and optimized job description templates for specific, hard-to-fill technical and professional roles. Saves hiring managers time and attracts better candidates." sometime, i want to just change the title, descriprion a bit before saving. So, just make it editable directly in the text, dont add fancy edit btn.

--------------

can u make this prompt editable that i can edit and save? create its table in supabase please.
"src\services\ideaGenerationService.ts" for both the system admin and user prompt. so add it next to 'Generate Ideas' btn.

------------

now i also want a db where i can insert ideas that i always want to exclude. So please allow me to exclude ideas in the modal where it give me the ideas output in /admin 's modal after i click on generate ideas. I want to be able to exclude ideas that i dont want to see again. So store it in the exclude list in table. Well u decide what to name the table. And then update src\services\ideaGenerationService.ts also.


------------

listen, in this project, we have /admin, and we see a list of pending item to be processed, right? there is a btn called 'generate', i want to also generate a landing page of the homepage using cloudflare flux on how the tool will look like when generating the homepage. U understand? please find official docs on how to use cloudflare flux and implement it. Use the best modal for image generation. And i will provide my API key. I will store it in .env file. The goal is to generate the image by writing its prompt, and then once generated, store it in DB and display it also so the developer can see the full requirements along its landing page image so they can develop it.

-------------

all work, please include the image in the http://localhost:8088/project/83aab514-6366-46eb-a296-3441a5b0550d now.

Just below the title and desvription below"

"Smart Home Automation Platform
Build a web platform that allows users to add, monitor, and create automation rules for their (virtual) smart home devices. The core of the app is a dashboard that displays all devices and their statuses, and a rule engine to create simple 'if-this-then-that' automations."


--------------

in http://localhost:8088/admin, when i click on 'generate' btn, i dont want to generate the image for the landing page for now, please comment it out in this flow. I now want another tab in /admin that will have all projects that has not yet have a landing page generated.

So currently we have pending, ai generated tab. Add a new one please. And there, add a btn called 'generate landing page' or something. And thats where the img generation flow should be.


----------------


XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX - stable-diffusion-v1-5-img2img - use image to image sometime later: https://developers.cloudflare.com/workers-ai/models/stable-diffusion-v1-5-img2img/


------------

in http://localhost:8088/project/a01d3c87-ce7d-45ec-993f-99210b0fca5a, in the 'homepage' section, 

--------------