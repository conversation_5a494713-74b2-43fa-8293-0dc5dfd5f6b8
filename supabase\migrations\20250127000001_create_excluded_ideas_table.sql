-- Create excluded_ideas table for storing ideas that should not be generated again
CREATE TABLE public.excluded_ideas (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT,
  target_user TEXT,
  reason TEXT, -- Optional reason for exclusion
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.excluded_ideas ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for now (since this is internal)
CREATE POLICY "Allow all operations on excluded_ideas" 
ON public.excluded_ideas 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_excluded_ideas_updated_at
  BEFORE UPDATE ON public.excluded_ideas
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Create index on title for faster lookups
CREATE INDEX idx_excluded_ideas_title ON public.excluded_ideas (title);

-- Create index on title and category combination for better filtering
CREATE INDEX idx_excluded_ideas_title_category ON public.excluded_ideas (title, category);
