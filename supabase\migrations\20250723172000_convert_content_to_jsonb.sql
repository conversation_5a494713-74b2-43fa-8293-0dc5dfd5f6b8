-- Convert content column from TEXT to JSONB
-- First, we'll create a backup of existing content and then convert it

-- Step 1: Add a temporary column to store the converted JSON data
ALTER TABLE public.projects ADD COLUMN content_json JSONB;

-- Step 2: Convert existing text content to JSON format
-- For existing text content, we'll wrap it in a JSON object with a "description" field
UPDATE public.projects 
SET content_json = CASE 
  WHEN content IS NOT NULL AND content != '' THEN 
    jsonb_build_object('description', content)
  ELSE 
    NULL
END;

-- Step 3: Drop the old content column
ALTER TABLE public.projects DROP COLUMN content;

-- Step 4: Rename the new column to content
ALTER TABLE public.projects RENAME COLUMN content_json TO content;

-- Step 5: Add a comment to document the JSON structure
COMMENT ON COLUMN public.projects.content IS 'JSON object containing structured project details like features, requirements, tech_stack, etc.';

-- Step 6: Create an index on the content JSONB column for better query performance
CREATE INDEX idx_projects_content_gin ON public.projects USING GIN (content);

-- Step 7: Update sample data with proper JSON structure
UPDATE public.projects 
SET content = jsonb_build_object(
  'description', COALESCE(content->>'description', ''),
  'features', ARRAY[]::text[],
  'requirements', ARRAY[]::text[],
  'tech_stack', ARRAY[]::text[],
  'difficulty', 'Medium',
  'estimated_hours', 40
)
WHERE content IS NOT NULL;
