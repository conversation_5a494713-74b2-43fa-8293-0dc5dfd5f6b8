import React, { useState, useEffect } from "react";
import { ProjectList } from "@/components/ProjectList";
import { SidebarProvider, Sidebar, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { BookOpen } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Project } from "@/types/project";

const Index = () => {
  const [statusFilter, setStatusFilter] = useState("Unclaimed");
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectStats, setProjectStats] = useState({
    totalPaidAmount: 0,
    totalAvailableAmount: 0
  });

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from("projects")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;

      const projectsWithPricing = (data || []).map(project => ({
        ...project,
        pricing_tier: (project as any).pricing_tier || 100,
        content: project.content as any
      })) as Project[];

      setProjects(projectsWithPricing);
      calculateStats(projectsWithPricing);
    } catch (error) {
      console.error("Error fetching projects:", error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const calculateStats = (projectList: Project[]) => {
    const completed = projectList.filter(p => p.status === "Completed");
    const available = projectList.filter(p => p.status === "Unclaimed" || p.status === "In Progress");

    const totalPaidAmount = completed.reduce((sum, project) => sum + (project.pricing_tier || 100), 0);
    const totalAvailableAmount = available.reduce((sum, project) => sum + (project.pricing_tier || 100), 0);

    const stats = {
      totalPaidAmount,
      totalAvailableAmount
    };

    setProjectStats(stats);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <Sidebar side="left" variant="sidebar" collapsible="offcanvas">
          <ProjectList
            renderSidebar={true}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
          />
        </Sidebar>
        <SidebarInset>
          <div className="min-h-screen bg-background">
            <div className="p-4 border-b border-border bg-gradient-to-r from-slate-50 to-gray-50">
              <div className="max-w-7xl mx-auto">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <SidebarTrigger className="h-8 w-8" />
                    <h1 className="text-2xl font-bold">Backlog</h1>
                  </div>

                  {/* Project Statistics - Center */}
                  <div className="flex items-center gap-8">
                    <div>Total Paid: {formatCurrency(projectStats.totalPaidAmount)}</div>
                    <div>Available Budget: {formatCurrency(projectStats.totalAvailableAmount)}</div>
                  </div>

                  {/* Private Tips Button - Eye-catching design */}
                  <div className="relative">
                    <Button
                      onClick={() => window.open('/tips-login', '_blank')}
                      className="private-tips-button relative overflow-hidden bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 text-white font-bold px-8 py-3 rounded-full shadow-2xl hover:shadow-purple-500/25 transform hover:scale-110 transition-all duration-300 border-2 border-white/20"
                    >
                      <div className="relative flex items-center gap-2 z-10">
                        <BookOpen className="h-5 w-5" />
                        <span className="font-bold text-lg">Trick & Tips to develop faster!</span>
                      </div>

                      {/* Shimmer overlay */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 animate-pulse"></div>
                    </Button>

                    {/* Floating badge */}
                    <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full animate-bounce shadow-lg">
                      Private
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <ProjectList
              renderSidebar={false}
              statusFilter={statusFilter}
              setStatusFilter={setStatusFilter}
            />
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default Index;
