import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Project } from "@/types/project";
import { FlexibleJsonRenderer } from "@/components/FlexibleJsonRenderer";

interface ProjectModalProps {
  project: Project | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ProjectModal({ project, isOpen, onClose }: ProjectModalProps) {
  if (!project) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">{project.name}</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <div>
            <h3 className="font-semibold text-sm uppercase tracking-wide text-muted-foreground">Description</h3>
            <p className="mt-1">{project.description}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-sm uppercase tracking-wide text-muted-foreground">Category</h3>
              <p className="mt-1">{project.category}</p>
            </div>
            <div>
              <h3 className="font-semibold text-sm uppercase tracking-wide text-muted-foreground">Target User</h3>
              <p className="mt-1">{project.target_user}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-sm uppercase tracking-wide text-muted-foreground">Status</h3>
              <p className="mt-1">{project.status}</p>
            </div>
            <div>
              <h3 className="font-semibold text-sm uppercase tracking-wide text-muted-foreground">Claimed By</h3>
              <p className="mt-1">{project.claimed_by || "—"}</p>
            </div>
          </div>

          {project.content && (
            <div className="space-y-4">
              <FlexibleJsonRenderer
                data={project.content}
                title="Project Details"
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}