# Project Ideas Platform

[![React](https://img.shields.io/badge/React-18-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue.svg)](https://www.typescriptlang.org/)
[![Supabase](https://img.shields.io/badge/Supabase-Latest-green.svg)](https://supabase.io/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3-blueviolet.svg)](https://tailwindcss.com/)
[![Vite](https://img.shields.io/badge/Vite-Latest-purple.svg)](https://vitejs.dev/)

A platform for managing and showcasing SaaS project ideas, designed to help build a portfolio of 100+ SaaS projects on a budget.

## 🚀 Features

### Public Features
- Browse a catalog of SaaS project ideas
- View detailed project information including descriptions, categories, and target users
- See project status (Claimed/Unclaimed) and who has claimed each project
- Explore detailed project specifications including features, requirements, and tech stack

### Admin Features
- Secure admin login system
- Add new project ideas with comprehensive details
- Edit existing projects
- Delete projects
- Generate detailed project specifications using Google's Gemini AI
- View project content in a structured format

## 🛠️ Tech Stack

- **Frontend**: React with TypeScript
- **Styling**: Tailwind CSS with Shadcn UI components
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Custom authentication with localStorage
- **State Management**: React Query
- **Routing**: React Router
- **AI Integration**: Google Gemini API for project content generation
- **Diagramming**: Mermaid for flow visualization

## 📋 Project Structure

```
Project_Ideas/
├── src/
│   ├── components/       # UI components
│   ├── hooks/            # Custom React hooks
│   ├── integrations/     # Third-party integrations
│   ├── pages/            # Page components
│   ├── services/         # Service modules
│   ├── types/            # TypeScript type definitions
│   └── App.tsx           # Main application component
├── supabase/
│   └── migrations/       # Database migration files
├── docs/                 # Documentation files
└── public/               # Static assets
```

## 🔧 Setup & Installation

1. **Clone the repository**

```bash
git clone <repository-url>
cd Project_Ideas
```

2. **Install dependencies**

```bash
npm install
# or
yarn install
# or
bun install
```

3. **Environment Setup**

Copy the `.env.example` file to `.env` and fill in your Supabase credentials and Gemini API key:

```
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
VITE_GEMINI_API_KEY=your-gemini-api-key
```

4. **Start the development server**

```bash
npm run dev
# or
yarn dev
# or
bun dev
```

## 🔐 Admin Access

To access the admin panel, navigate to `/admin/login` and use the following credentials:

- **Username**: antish
- **Password**: kshamta

## 📊 Database Schema

The application uses a single `projects` table with the following structure:

- `id`: UUID (Primary Key)
- `name`: Text (Project name)
- `description`: Text (Short project description)
- `category`: Text (Project category)
- `target_user`: Text (Target audience)
- `status`: Text (Claimed/Unclaimed)
- `claimed_by`: Text (Person who claimed the project)
- `content`: JSONB (Detailed project specifications)
- `created_at`: Timestamp
- `updated_at`: Timestamp

## 🧩 Key Components

- **ProjectList**: Displays all projects in a table format
- **ProjectModal**: Shows detailed project information in a modal
- **AdminPanel**: Interface for managing projects
- **ProjectContentEditor**: Editor for structured project content
- **FlexibleJsonRenderer**: Renders JSON data in a user-friendly format
- **MermaidDiagram**: Displays project flow diagrams

## 🤖 AI Integration

The platform integrates with Google's Gemini AI to generate comprehensive project specifications, including:

- Project descriptions
- Subscription plans
- User flow features
- Database structure
- Platform usage guidelines
- Flow diagrams

## 📝 License

This project is part of a portfolio of SaaS applications built on a budget. Each project is designed to be completed within a $100 budget and includes basic SaaS features.

## 🔗 Related Resources

- [Features Documentation](./docs/features.md)
- [Project Ideas List](./docs/projects.md)
- [Definition of Done](./docs/definition-of-done.md)
- [Development Tips](./docs/tips.md)