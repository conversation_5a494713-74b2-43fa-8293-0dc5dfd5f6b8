import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2, RefreshCw, Check, Wand2 } from 'lucide-react';
import { ProjectVariation } from '@/services/ideaGenerationService';

interface ProjectVariationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  variations: ProjectVariation[];
  isLoading: boolean;
  onSelectVariation: (variation: ProjectVariation) => void;
  onGenerateVariations: (customInstruction: string) => void;
  originalTitle: string;
  originalDescription: string;
}

export function ProjectVariationsModal({
  isOpen,
  onClose,
  variations,
  isLoading,
  onSelectVariation,
  onGenerateVariations,
  originalTitle,
  originalDescription
}: ProjectVariationsModalProps) {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [customInstruction, setCustomInstruction] = useState<string>('');

  const handleSelect = (index: number) => {
    setSelectedIndex(index);
  };

  const handleConfirmSelection = () => {
    if (selectedIndex !== null) {
      onSelectVariation(variations[selectedIndex]);
      setSelectedIndex(null);
      onClose();
    }
  };

  const handleClose = () => {
    setSelectedIndex(null);
    setCustomInstruction('');
    onClose();
  };

  const handleGenerateWithInstruction = () => {
    onGenerateVariations(customInstruction);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 text-blue-500" />
            Project Variations
          </DialogTitle>
          <div className="text-sm text-gray-600 mt-2">
            <p><strong>Original:</strong> {originalTitle}</p>
            <p className="text-xs text-gray-500 mt-1">{originalDescription}</p>
          </div>
        </DialogHeader>

        {/* Custom Instruction Input */}
        <div className="space-y-4 border-b pb-4">
          <div className="space-y-2">
            <Label htmlFor="customInstruction" className="text-sm font-medium">
              Custom Instruction (Optional)
            </Label>
            <Textarea
              id="customInstruction"
              placeholder="Add any specific requirements or instructions for generating variations..."
              value={customInstruction}
              onChange={(e) => setCustomInstruction(e.target.value)}
              className="min-h-[80px]"
              disabled={isLoading}
            />
            <p className="text-xs text-gray-500">
              This instruction will be given highest priority when generating variations.
            </p>
          </div>
          <Button
            onClick={handleGenerateWithInstruction}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating Variations...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4 mr-2" />
                Generate Variations
              </>
            )}
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
              <p className="text-gray-600">Generating variations...</p>
              <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
            </div>
          </div>
        ) : variations.length > 0 ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-600">
                  Select a variation to replace the original project
                </span>
                {selectedIndex !== null && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    Variation {selectedIndex + 1} selected
                  </Badge>
                )}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleConfirmSelection}
                  disabled={selectedIndex === null}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Check className="h-4 w-4 mr-1" />
                  Update Project
                </Button>
              </div>
            </div>

            <div className="grid gap-4 max-h-[60vh] overflow-y-auto pr-2">
              {variations.map((variation, index) => (
                <Card
                  key={index}
                  className={`cursor-pointer transition-all ${
                    selectedIndex === index
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:shadow-md hover:bg-gray-50'
                  }`}
                  onClick={() => handleSelect(index)}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-semibold">{variation.title}</span>
                        <Badge variant="outline" className="text-xs">
                          Variation {index + 1}
                        </Badge>
                      </div>
                      {selectedIndex === index && (
                        <Check className="h-5 w-5 text-blue-600" />
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 leading-relaxed">
                      {variation.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No variations generated yet.</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
