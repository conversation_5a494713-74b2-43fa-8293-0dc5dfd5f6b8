import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ProjectContentEditor } from '@/components/ProjectContentEditor';
import { FlexibleJsonRenderer } from '@/components/FlexibleJsonRenderer';
import { ProjectContent, normalizeProjectContent } from '@/types/project';
import { AlertCircle, Eye, Code } from 'lucide-react';

interface JsonContentEditorProps {
  content: any;
  onChange: (content: any) => void;
}

export function JsonContentEditor({ content, onChange }: JsonContentEditorProps) {
  const [jsonText, setJsonText] = useState('');
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('visual');

  // Check if content is AI-generated (has MVPResponse structure) or basic ProjectContent
  const isAIGenerated = content && typeof content === 'object' && 
    (content.project_description || content.main_user_features || content.homepage);

  useEffect(() => {
    if (content) {
      setJsonText(JSON.stringify(content, null, 2));
    } else {
      setJsonText('{}');
    }
  }, [content]);

  const handleJsonChange = (value: string) => {
    setJsonText(value);
    setJsonError(null);

    try {
      const parsed = JSON.parse(value);
      onChange(parsed);
    } catch (error) {
      setJsonError(error instanceof Error ? error.message : 'Invalid JSON');
    }
  };

  const handleBasicContentChange = (basicContent: ProjectContent) => {
    onChange(basicContent);
  };

  if (isAIGenerated) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-blue-600">
          <AlertCircle className="h-4 w-4" />
          <span>This project contains AI-generated content</span>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="visual" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="json" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Edit JSON
            </TabsTrigger>
            <TabsTrigger value="basic">Basic Edit</TabsTrigger>
          </TabsList>

          <TabsContent value="visual" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Content Preview</CardTitle>
              </CardHeader>
              <CardContent className="max-h-[400px] overflow-y-auto">
                <FlexibleJsonRenderer data={content} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="json" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">JSON Editor</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={jsonText}
                  onChange={(e) => handleJsonChange(e.target.value)}
                  placeholder="Enter JSON content..."
                  className="min-h-[300px] max-h-[400px] font-mono text-sm resize-none"
                />
                {jsonError && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      JSON Error: {jsonError}
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="basic" className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Warning: Switching to basic edit mode will convert this AI-generated content to a simple project structure and lose the rich data.
              </AlertDescription>
            </Alert>
            <ProjectContentEditor
              content={normalizeProjectContent(content)}
              onChange={handleBasicContentChange}
            />
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  // For basic project content, use the original ProjectContentEditor
  return (
    <ProjectContentEditor
      content={normalizeProjectContent(content)}
      onChange={handleBasicContentChange}
    />
  );
}
