# Steps

- Build the frontend UI based on the project you chose (header, body, footer, and subscription packages) plus the admin UI if needed. Use fewer icons and avoid the typical AI-style design, try to make the UI look unique.
- Send me a screenshot of the UI for approval.
- After I approve, you can continue with the rest.

# Tips (not my requirement, but for your own good, your choice)
- Create a Mermaid diagram to show the project flow.
- Add relevant .md files to help track the work and avoid AI hallucinations.
- Use useful MCPs like sequential-thinking, Supabase, Task Management, and others where needed.
